package main

import (
	"fmt"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/pkg/doodstream"
	"magnet-downloader/pkg/logger"
)

// ConcurrentTestResult 并发测试结果
type ConcurrentTestResult struct {
	Concurrency    int           `json:"concurrency"`
	TotalRequests  int           `json:"total_requests"`
	SuccessCount   int64         `json:"success_count"`
	FailureCount   int64         `json:"failure_count"`
	TotalDuration  time.Duration `json:"total_duration"`
	AvgResponseTime time.Duration `json:"avg_response_time"`
	RequestsPerSec float64       `json:"requests_per_sec"`
	ErrorRate      float64       `json:"error_rate"`
}

func main() {
	fmt.Println("🧪 DoodStream并发性能测试")
	fmt.Println("========================")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("❌ 加载配置失败: %v\n", err)
		return
	}

	// 创建DoodStream客户端
	doodStreamConfig := &doodstream.Config{
		APIKey:     cfg.FileProcessing.DoodStream.APIKey,
		BaseURL:    cfg.FileProcessing.DoodStream.BaseURL,
		Timeout:    time.Duration(cfg.FileProcessing.DoodStream.Timeout) * time.Second,
		MaxRetries: cfg.FileProcessing.DoodStream.MaxRetries,
	}

	client := doodstream.NewClient(doodStreamConfig)

	// 测试连接
	fmt.Println("🔗 测试DoodStream连接...")
	if err := client.TestConnection(); err != nil {
		fmt.Printf("❌ DoodStream连接失败: %v\n", err)
		return
	}
	fmt.Println("✅ DoodStream连接成功")

	// 测试不同并发级别
	concurrencyLevels := []int{1, 3, 5, 8, 10, 15, 20}
	requestsPerLevel := 20 // 每个并发级别测试20个请求

	var results []ConcurrentTestResult

	for _, concurrency := range concurrencyLevels {
		fmt.Printf("\n📊 测试并发数: %d (每个级别 %d 个请求)\n", concurrency, requestsPerLevel)
		
		result := testConcurrentRequests(client, concurrency, requestsPerLevel)
		results = append(results, result)
		
		printConcurrentResult(result)
		
		// 等待一段时间避免API限制
		if concurrency < len(concurrencyLevels) {
			fmt.Println("⏳ 等待10秒避免API限制...")
			time.Sleep(10 * time.Second)
		}
	}

	// 生成并发测试报告
	fmt.Println("\n📈 并发性能测试报告")
	generateConcurrentReport(results)

	// 分析最优并发数
	fmt.Println("\n🎯 并发优化建议")
	analyzeConcurrentPerformance(results)
}

// testConcurrentRequests 测试并发请求
func testConcurrentRequests(client *doodstream.Client, concurrency, totalRequests int) ConcurrentTestResult {
	var successCount, failureCount int64
	var totalResponseTime int64
	
	result := ConcurrentTestResult{
		Concurrency:   concurrency,
		TotalRequests: totalRequests,
	}

	// 创建信号量控制并发数
	semaphore := make(chan struct{}, concurrency)
	var wg sync.WaitGroup

	startTime := time.Now()

	// 启动并发请求
	for i := 0; i < totalRequests; i++ {
		wg.Add(1)
		go func(requestID int) {
			defer wg.Done()
			
			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 执行请求（这里使用连接测试作为轻量级请求）
			requestStart := time.Now()
			err := client.TestConnection()
			requestDuration := time.Since(requestStart)

			// 累加响应时间
			atomic.AddInt64(&totalResponseTime, int64(requestDuration))

			if err != nil {
				atomic.AddInt64(&failureCount, 1)
				logger.Debugf("Request %d failed: %v", requestID, err)
			} else {
				atomic.AddInt64(&successCount, 1)
				logger.Debugf("Request %d succeeded in %v", requestID, requestDuration)
			}
		}(i)
	}

	wg.Wait()
	result.TotalDuration = time.Since(startTime)

	// 计算统计数据
	result.SuccessCount = successCount
	result.FailureCount = failureCount
	
	if totalRequests > 0 {
		result.ErrorRate = float64(failureCount) / float64(totalRequests) * 100
		result.RequestsPerSec = float64(totalRequests) / result.TotalDuration.Seconds()
		
		if successCount > 0 {
			avgResponseTimeNs := totalResponseTime / successCount
			result.AvgResponseTime = time.Duration(avgResponseTimeNs)
		}
	}

	return result
}

// printConcurrentResult 打印并发测试结果
func printConcurrentResult(result ConcurrentTestResult) {
	fmt.Printf("   总请求数: %d\n", result.TotalRequests)
	fmt.Printf("   成功请求: %d\n", result.SuccessCount)
	fmt.Printf("   失败请求: %d\n", result.FailureCount)
	fmt.Printf("   错误率: %.2f%%\n", result.ErrorRate)
	fmt.Printf("   总耗时: %v\n", result.TotalDuration)
	fmt.Printf("   平均响应时间: %v\n", result.AvgResponseTime)
	fmt.Printf("   请求/秒: %.2f\n", result.RequestsPerSec)
	
	// 性能评估
	if result.ErrorRate == 0 {
		fmt.Printf("   ✅ 稳定性: 优秀\n")
	} else if result.ErrorRate < 5 {
		fmt.Printf("   ⚠️  稳定性: 良好\n")
	} else {
		fmt.Printf("   ❌ 稳定性: 需要改进\n")
	}
}

// generateConcurrentReport 生成并发测试报告
func generateConcurrentReport(results []ConcurrentTestResult) {
	fmt.Println(strings.Repeat("=", 80))
	fmt.Printf("%-8s %-10s %-10s %-12s %-15s %-12s\n", 
		"并发数", "成功率", "错误率", "请求/秒", "平均响应时间", "稳定性")
	fmt.Println(strings.Repeat("-", 80))

	for _, result := range results {
		successRate := float64(result.SuccessCount) / float64(result.TotalRequests) * 100
		stability := "优秀"
		if result.ErrorRate > 0 {
			if result.ErrorRate < 5 {
				stability = "良好"
			} else {
				stability = "差"
			}
		}

		fmt.Printf("%-8d %-10.1f%% %-10.2f%% %-12.2f %-15v %-12s\n",
			result.Concurrency,
			successRate,
			result.ErrorRate,
			result.RequestsPerSec,
			result.AvgResponseTime,
			stability,
		)
	}
}

// analyzeConcurrentPerformance 分析并发性能
func analyzeConcurrentPerformance(results []ConcurrentTestResult) {
	if len(results) == 0 {
		return
	}

	// 找到最佳并发数
	var bestResult ConcurrentTestResult
	bestScore := 0.0

	for _, result := range results {
		// 综合评分：请求/秒 * 成功率 * (1 - 错误率/100)
		successRate := float64(result.SuccessCount) / float64(result.TotalRequests)
		errorPenalty := 1.0 - (result.ErrorRate / 100.0)
		score := result.RequestsPerSec * successRate * errorPenalty

		if score > bestScore {
			bestScore = score
			bestResult = result
		}
	}

	fmt.Printf("🏆 推荐最优并发数: %d\n", bestResult.Concurrency)
	fmt.Printf("   预期请求/秒: %.2f\n", bestResult.RequestsPerSec)
	fmt.Printf("   预期错误率: %.2f%%\n", bestResult.ErrorRate)
	fmt.Printf("   平均响应时间: %v\n", bestResult.AvgResponseTime)

	// 性能分析和建议
	fmt.Println("\n💡 性能分析:")
	
	// 检查错误率趋势
	highErrorResults := 0
	for _, result := range results {
		if result.ErrorRate > 5 {
			highErrorResults++
		}
	}

	if highErrorResults > len(results)/2 {
		fmt.Println("   ⚠️  高并发下错误率较高，建议:")
		fmt.Println("      - 降低并发数")
		fmt.Println("      - 增加重试次数")
		fmt.Println("      - 调整速率限制参数")
	}

	// 检查响应时间趋势
	if bestResult.AvgResponseTime > 5*time.Second {
		fmt.Println("   ⏱️  响应时间较长，建议:")
		fmt.Println("      - 检查网络连接")
		fmt.Println("      - 增加超时时间")
		fmt.Println("      - 考虑降低并发数")
	}

	// 检查吞吐量
	if bestResult.RequestsPerSec < 5 {
		fmt.Println("   📈 吞吐量较低，建议:")
		fmt.Println("      - 优化速率限制")
		fmt.Println("      - 检查API限制")
		fmt.Println("      - 考虑增加并发数")
	}

	fmt.Println("\n🔧 配置建议:")
	fmt.Printf("   建议在配置中设置最大并发数为: %d\n", bestResult.Concurrency)
	fmt.Printf("   建议速率限制间隔: %dms\n", 1000/int(bestResult.RequestsPerSec))
}