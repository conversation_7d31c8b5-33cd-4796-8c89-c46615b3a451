package tests

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"testing"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/logger"
)

// BenchmarkFileProcessing 文件处理性能基准测试
func BenchmarkFileProcessing(b *testing.B) {
	logger.Init("error", "text") // 减少日志输出

	// 创建测试文件
	testDir := "/tmp/benchmark_test"
	if err := os.MkdirAll(testDir, 0755); err != nil {
		b.Fatalf("Failed to create test directory: %v", err)
	}
	defer os.RemoveAll(testDir)

	// 创建不同大小的测试文件
	testSizes := []struct {
		name string
		size int
	}{
		{"1MB", 1 * 1024 * 1024},
		{"10MB", 10 * 1024 * 1024},
		{"100MB", 100 * 1024 * 1024},
	}

	for _, ts := range testSizes {
		testFile := filepath.Join(testDir, fmt.Sprintf("test_%s.dat", ts.name))
		testData := make([]byte, ts.size)
		for i := range testData {
			testData[i] = byte(i % 256)
		}
		
		if err := os.WriteFile(testFile, testData, 0644); err != nil {
			b.Fatalf("Failed to create test file %s: %v", ts.name, err)
		}

		b.Run(fmt.Sprintf("ProcessFile_%s", ts.name), func(b *testing.B) {
			config := &fileprocessor.ProcessingConfig{
				ChunkSizeMB:       1,
				EncryptionEnabled: true,
				KeepOriginal:      true,
				WorkDir:           filepath.Join(testDir, "processing"),
			}

			processor := fileprocessor.NewProcessor(config)

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				result, err := processor.ProcessFile(testFile, nil)
				if err != nil {
					b.Fatalf("Processing failed: %v", err)
				}
				
				// 清理工作目录
				if result.WorkDir != "" {
					os.RemoveAll(result.WorkDir)
				}
			}
		})
	}
}

// BenchmarkConcurrentProcessing 并发处理性能测试
func BenchmarkConcurrentProcessing(b *testing.B) {
	logger.Init("error", "text")

	testDir := "/tmp/concurrent_benchmark"
	if err := os.MkdirAll(testDir, 0755); err != nil {
		b.Fatalf("Failed to create test directory: %v", err)
	}
	defer os.RemoveAll(testDir)

	// 创建测试文件
	testFile := filepath.Join(testDir, "test_concurrent.dat")
	testData := make([]byte, 5*1024*1024) // 5MB
	for i := range testData {
		testData[i] = byte(i % 256)
	}
	
	if err := os.WriteFile(testFile, testData, 0644); err != nil {
		b.Fatalf("Failed to create test file: %v", err)
	}

	concurrencyLevels := []int{1, 2, 4, 8}

	for _, concurrency := range concurrencyLevels {
		b.Run(fmt.Sprintf("Concurrency_%d", concurrency), func(b *testing.B) {
			b.ResetTimer()
			
			for i := 0; i < b.N; i++ {
				var wg sync.WaitGroup
				
				for j := 0; j < concurrency; j++ {
					wg.Add(1)
					go func(workerID int) {
						defer wg.Done()
						
						config := &fileprocessor.ProcessingConfig{
							ChunkSizeMB:       1,
							EncryptionEnabled: true,
							KeepOriginal:      true,
							WorkDir:           filepath.Join(testDir, fmt.Sprintf("processing_%d", workerID)),
						}

						processor := fileprocessor.NewProcessor(config)
						result, err := processor.ProcessFile(testFile, nil)
						if err != nil {
							b.Errorf("Worker %d processing failed: %v", workerID, err)
							return
						}
						
						// 清理工作目录
						if result.WorkDir != "" {
							os.RemoveAll(result.WorkDir)
						}
					}(j)
				}
				
				wg.Wait()
			}
		})
	}
}

// TestMemoryUsage 内存使用测试
func TestMemoryUsage(t *testing.T) {
	logger.Init("error", "text")

	testDir := "/tmp/memory_test"
	if err := os.MkdirAll(testDir, 0755); err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}
	defer os.RemoveAll(testDir)

	// 创建大文件测试内存使用
	testFile := filepath.Join(testDir, "large_test.dat")
	testData := make([]byte, 50*1024*1024) // 50MB
	for i := range testData {
		testData[i] = byte(i % 256)
	}
	
	if err := os.WriteFile(testFile, testData, 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	config := &fileprocessor.ProcessingConfig{
		ChunkSizeMB:       1,
		EncryptionEnabled: true,
		KeepOriginal:      true,
		WorkDir:           filepath.Join(testDir, "processing"),
	}

	processor := fileprocessor.NewProcessor(config)

	// 记录开始时间和内存
	startTime := time.Now()
	
	result, err := processor.ProcessFile(testFile, func(stage string, progress float64, message string) {
		t.Logf("Progress: %s %.1f%% - %s", stage, progress, message)
	})
	
	processingTime := time.Since(startTime)
	
	if err != nil {
		t.Fatalf("Processing failed: %v", err)
	}

	// 验证结果
	if result.ChunkCount == 0 {
		t.Error("Expected chunks to be created")
	}

	if result.TotalSize != int64(len(testData)) {
		t.Errorf("Expected total size %d, got %d", len(testData), result.TotalSize)
	}

	t.Logf("Processing completed: %d chunks, %v", result.ChunkCount, processingTime)
	t.Logf("Processing speed: %.2f MB/s", float64(len(testData))/1024/1024/processingTime.Seconds())

	// 清理
	if result.WorkDir != "" {
		os.RemoveAll(result.WorkDir)
	}
}

// TestActualFilesPerformance 测试ActualFiles性能
func TestActualFilesPerformance(t *testing.T) {
	// 创建大量文件信息
	files := make([]model.ActualFileInfo, 1000)
	for i := range files {
		files[i] = model.ActualFileInfo{
			Index:           i,
			Path:            fmt.Sprintf("/downloads/file_%d.mp4", i),
			Name:            fmt.Sprintf("file_%d.mp4", i),
			Size:            int64(1024 * 1024 * (i + 1)),
			CompletedLength: int64(1024 * 1024 * (i + 1)),
			Selected:        i%2 == 0, // 一半选中
		}
	}

	task := &model.DownloadTask{}
	
	// 测试设置性能
	startTime := time.Now()
	task.SetActualFiles(files)
	setTime := time.Since(startTime)
	
	// 测试获取性能
	startTime = time.Now()
	allFiles := task.GetActualFiles()
	getTime := time.Since(startTime)
	
	// 测试过滤性能
	startTime = time.Now()
	selectedFiles := task.GetSelectedFiles()
	filterTime := time.Since(startTime)

	t.Logf("Performance results for 1000 files:")
	t.Logf("  SetActualFiles: %v", setTime)
	t.Logf("  GetActualFiles: %v", getTime)
	t.Logf("  GetSelectedFiles: %v", filterTime)
	t.Logf("  Total files: %d", len(allFiles))
	t.Logf("  Selected files: %d", len(selectedFiles))

	// 验证结果正确性
	if len(allFiles) != 1000 {
		t.Errorf("Expected 1000 files, got %d", len(allFiles))
	}

	if len(selectedFiles) != 500 {
		t.Errorf("Expected 500 selected files, got %d", len(selectedFiles))
	}
}

// TestDatabasePerformance 测试数据库操作性能
func TestDatabasePerformance(t *testing.T) {
	// 这个测试需要真实的数据库连接，这里只是示例结构
	t.Skip("Database performance test requires real database connection")

	// 示例：测试大量任务的数据库操作性能
	// 可以在集成测试中实现
}

// TestErrorRecoveryPerformance 测试错误恢复性能
func TestErrorRecoveryPerformance(t *testing.T) {
	logger.Init("error", "text")

	testDir := "/tmp/error_recovery_test"
	if err := os.MkdirAll(testDir, 0755); err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}
	defer os.RemoveAll(testDir)

	// 测试处理不存在文件的错误恢复时间
	nonExistentFile := filepath.Join(testDir, "nonexistent.dat")

	config := &fileprocessor.ProcessingConfig{
		ChunkSizeMB:       1,
		EncryptionEnabled: true,
		WorkDir:           filepath.Join(testDir, "processing"),
	}

	processor := fileprocessor.NewProcessor(config)

	// 测试错误处理时间
	errorCount := 100
	startTime := time.Now()

	for i := 0; i < errorCount; i++ {
		_, err := processor.ProcessFile(nonExistentFile, nil)
		if err == nil {
			t.Error("Expected error for nonexistent file")
		}
	}

	totalTime := time.Since(startTime)
	avgTime := totalTime / time.Duration(errorCount)

	t.Logf("Error recovery performance:")
	t.Logf("  Total time for %d errors: %v", errorCount, totalTime)
	t.Logf("  Average time per error: %v", avgTime)

	// 验证错误处理时间合理（应该很快）
	if avgTime > 10*time.Millisecond {
		t.Errorf("Error handling too slow: %v per error", avgTime)
	}
}