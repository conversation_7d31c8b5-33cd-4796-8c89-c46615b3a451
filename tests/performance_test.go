package main

import (
	"fmt"
	"math/rand"
	"os"
	"path/filepath"
	"sync"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/pkg/doodstream"
	"magnet-downloader/pkg/imgbb"
	"magnet-downloader/pkg/logger"
)

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	Concurrency     int           `json:"concurrency"`
	TotalFiles      int           `json:"total_files"`
	SuccessCount    int           `json:"success_count"`
	FailureCount    int           `json:"failure_count"`
	TotalSize       int64         `json:"total_size"`
	Duration        time.Duration `json:"duration"`
	Throughput      float64       `json:"throughput_mbps"`
	AvgFileTime     time.Duration `json:"avg_file_time"`
	BandwidthUtil   float64       `json:"bandwidth_utilization"`
}

// TestFile 测试文件信息
type TestFile struct {
	Path string
	Size int64
	Name string
}

const (
	// 2.5G带宽 = 2500 Mbps = 312.5 MB/s
	MaxBandwidthMBps = 312.5
	TestDataDir      = "/tmp/doodstream_test_data"
)

func main() {
	fmt.Println("🚀 DoodStream性能测试和并发优化")
	fmt.Printf("📊 目标带宽: %.1f Mbps (%.1f MB/s)\n", MaxBandwidthMBps*8, MaxBandwidthMBps)

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("❌ 加载配置失败: %v\n", err)
		return
	}

	// 创建测试文件
	fmt.Println("\n📁 准备测试文件...")
	testFiles, err := createTestFiles()
	if err != nil {
		fmt.Printf("❌ 创建测试文件失败: %v\n", err)
		return
	}
	defer cleanupTestFiles()

	fmt.Printf("✅ 创建了 %d 个测试文件\n", len(testFiles))

	// 测试不同并发级别
	concurrencyLevels := []int{1, 3, 5, 8, 10}
	
	fmt.Println("\n🧪 开始性能测试...")
	
	var allMetrics []PerformanceMetrics

	for _, concurrency := range concurrencyLevels {
		fmt.Printf("\n📈 测试并发数: %d\n", concurrency)
		
		// 测试DoodStream上传
		if cfg.FileProcessing.UploadProvider == "doodstream" || true {
			metrics := testDoodStreamUpload(cfg, testFiles, concurrency)
			allMetrics = append(allMetrics, metrics)
			printMetrics("DoodStream", metrics)
		}
		
		// 等待一段时间避免API限制
		if concurrency < len(concurrencyLevels) {
			fmt.Println("⏳ 等待30秒避免API限制...")
			time.Sleep(30 * time.Second)
		}
	}

	// 生成性能报告
	fmt.Println("\n📊 性能测试报告")
	generatePerformanceReport(allMetrics)

	// 推荐最优配置
	fmt.Println("\n💡 性能优化建议")
	recommendOptimalSettings(allMetrics)
}

// createTestFiles 创建测试文件
func createTestFiles() ([]TestFile, error) {
	if err := os.MkdirAll(TestDataDir, 0755); err != nil {
		return nil, err
	}

	var testFiles []TestFile
	
	// 创建不同大小的测试文件
	fileSizes := []struct {
		name string
		size int64
	}{
		{"small_10mb.dat", 10 * 1024 * 1024},      // 10MB
		{"medium_50mb.dat", 50 * 1024 * 1024},     // 50MB
		{"large_100mb.dat", 100 * 1024 * 1024},    // 100MB
		{"xlarge_200mb.dat", 200 * 1024 * 1024},   // 200MB
	}

	for _, fileInfo := range fileSizes {
		filePath := filepath.Join(TestDataDir, fileInfo.name)
		
		// 检查文件是否已存在
		if _, err := os.Stat(filePath); os.IsNotExist(err) {
			if err := createRandomFile(filePath, fileInfo.size); err != nil {
				return nil, fmt.Errorf("创建文件 %s 失败: %v", fileInfo.name, err)
			}
		}

		testFiles = append(testFiles, TestFile{
			Path: filePath,
			Size: fileInfo.size,
			Name: fileInfo.name,
		})
	}

	return testFiles, nil
}

// createRandomFile 创建指定大小的随机文件
func createRandomFile(path string, size int64) error {
	file, err := os.Create(path)
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入随机数据
	buffer := make([]byte, 1024*1024) // 1MB buffer
	rand.Read(buffer)

	written := int64(0)
	for written < size {
		remaining := size - written
		writeSize := int64(len(buffer))
		if remaining < writeSize {
			writeSize = remaining
		}

		n, err := file.Write(buffer[:writeSize])
		if err != nil {
			return err
		}
		written += int64(n)
	}

	return nil
}

// testDoodStreamUpload 测试DoodStream上传性能
func testDoodStreamUpload(cfg *config.Config, testFiles []TestFile, concurrency int) PerformanceMetrics {
	// 创建DoodStream客户端
	doodStreamConfig := &doodstream.Config{
		APIKey:     cfg.FileProcessing.DoodStream.APIKey,
		BaseURL:    cfg.FileProcessing.DoodStream.BaseURL,
		Timeout:    time.Duration(cfg.FileProcessing.DoodStream.Timeout) * time.Second,
		MaxRetries: cfg.FileProcessing.DoodStream.MaxRetries,
	}

	client := doodstream.NewClient(doodStreamConfig)

	// 性能指标
	metrics := PerformanceMetrics{
		Concurrency: concurrency,
		TotalFiles:  len(testFiles),
	}

	// 计算总大小
	for _, file := range testFiles {
		metrics.TotalSize += file.Size
	}

	// 并发上传
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, concurrency)
	resultChan := make(chan bool, len(testFiles))

	startTime := time.Now()

	for _, file := range testFiles {
		wg.Add(1)
		go func(f TestFile) {
			defer wg.Done()
			
			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 上传文件
			fileStartTime := time.Now()
			_, err := client.UploadFile(f.Path)
			uploadDuration := time.Since(fileStartTime)

			if err != nil {
				logger.Errorf("上传文件 %s 失败: %v", f.Name, err)
				resultChan <- false
			} else {
				logger.Infof("上传文件 %s 成功，耗时: %v", f.Name, uploadDuration)
				resultChan <- true
			}
		}(file)
	}

	wg.Wait()
	close(resultChan)

	metrics.Duration = time.Since(startTime)

	// 统计结果
	for result := range resultChan {
		if result {
			metrics.SuccessCount++
		} else {
			metrics.FailureCount++
		}
	}

	// 计算性能指标
	if metrics.Duration > 0 {
		totalSizeMB := float64(metrics.TotalSize) / (1024 * 1024)
		metrics.Throughput = totalSizeMB / metrics.Duration.Seconds()
		metrics.BandwidthUtil = (metrics.Throughput / MaxBandwidthMBps) * 100
		
		if metrics.SuccessCount > 0 {
			metrics.AvgFileTime = metrics.Duration / time.Duration(metrics.SuccessCount)
		}
	}

	return metrics
}

// printMetrics 打印性能指标
func printMetrics(provider string, metrics PerformanceMetrics) {
	fmt.Printf("📊 %s 性能指标:\n", provider)
	fmt.Printf("   并发数: %d\n", metrics.Concurrency)
	fmt.Printf("   总文件数: %d\n", metrics.TotalFiles)
	fmt.Printf("   成功上传: %d\n", metrics.SuccessCount)
	fmt.Printf("   失败上传: %d\n", metrics.FailureCount)
	fmt.Printf("   总大小: %.2f MB\n", float64(metrics.TotalSize)/(1024*1024))
	fmt.Printf("   总耗时: %v\n", metrics.Duration)
	fmt.Printf("   平均吞吐量: %.2f MB/s\n", metrics.Throughput)
	fmt.Printf("   带宽利用率: %.1f%%\n", metrics.BandwidthUtil)
	fmt.Printf("   平均文件上传时间: %v\n", metrics.AvgFileTime)
	
	if metrics.FailureCount > 0 {
		successRate := float64(metrics.SuccessCount) / float64(metrics.TotalFiles) * 100
		fmt.Printf("   成功率: %.1f%%\n", successRate)
	}
}

// generatePerformanceReport 生成性能报告
func generatePerformanceReport(allMetrics []PerformanceMetrics) {
	fmt.Println("=" * 60)
	fmt.Printf("%-10s %-8s %-12s %-15s %-12s\n", "并发数", "成功率", "吞吐量(MB/s)", "带宽利用率", "平均耗时")
	fmt.Println("-" * 60)

	for _, metrics := range allMetrics {
		successRate := float64(metrics.SuccessCount) / float64(metrics.TotalFiles) * 100
		fmt.Printf("%-10d %-8.1f%% %-12.2f %-15.1f%% %-12v\n",
			metrics.Concurrency,
			successRate,
			metrics.Throughput,
			metrics.BandwidthUtil,
			metrics.AvgFileTime,
		)
	}
}

// recommendOptimalSettings 推荐最优设置
func recommendOptimalSettings(allMetrics []PerformanceMetrics) {
	if len(allMetrics) == 0 {
		return
	}

	// 找到最佳性能配置
	var bestMetrics PerformanceMetrics
	bestScore := 0.0

	for _, metrics := range allMetrics {
		successRate := float64(metrics.SuccessCount) / float64(metrics.TotalFiles)
		// 综合评分：成功率 * 吞吐量 * 带宽利用率
		score := successRate * metrics.Throughput * (metrics.BandwidthUtil / 100)
		
		if score > bestScore {
			bestScore = score
			bestMetrics = metrics
		}
	}

	fmt.Printf("🏆 推荐最优配置:\n")
	fmt.Printf("   最佳并发数: %d\n", bestMetrics.Concurrency)
	fmt.Printf("   预期吞吐量: %.2f MB/s\n", bestMetrics.Throughput)
	fmt.Printf("   带宽利用率: %.1f%%\n", bestMetrics.BandwidthUtil)
	
	// 速率限制建议
	if bestMetrics.BandwidthUtil > 80 {
		fmt.Printf("💡 建议调整速率限制:\n")
		fmt.Printf("   当前速率限制可能过于保守\n")
		fmt.Printf("   建议将并发数设置为: %d\n", bestMetrics.Concurrency)
	} else if bestMetrics.BandwidthUtil < 50 {
		fmt.Printf("⚠️  带宽利用率较低:\n")
		fmt.Printf("   可以考虑增加并发数以提高性能\n")
		fmt.Printf("   建议测试更高的并发数: %d+\n", bestMetrics.Concurrency+2)
	}
}

// cleanupTestFiles 清理测试文件
func cleanupTestFiles() {
	fmt.Println("\n🧹 清理测试文件...")
	if err := os.RemoveAll(TestDataDir); err != nil {
		fmt.Printf("⚠️  清理测试文件失败: %v\n", err)
	} else {
		fmt.Println("✅ 测试文件清理完成")
	}
}