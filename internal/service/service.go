package service

import (
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/repository"
	ws "magnet-downloader/internal/websocket"
	"magnet-downloader/pkg/database"
	"magnet-downloader/pkg/doodstream"
	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/imgbb"
	"magnet-downloader/pkg/queue"
	"magnet-downloader/pkg/streaming"
)

// Services 服务集合
type Services struct {
	Task           TaskService
	User           UserService
	Config         ConfigService
	Download       DownloadService
	Queue          QueueService
	Aria2          Aria2Service
	WebSocket      *ws.Service
	FileProcessing FileProcessingService
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	JWTSecret string
}

// NewServices 创建服务集合
func NewServices(repo repository.Repository, cfg *config.Config) *Services {
	// 创建队列
	redisQueue := queue.NewRedisQueue("magnet-downloader")

	// 创建基础服务
	queueService := NewQueueService(redisQueue)
	aria2Service := NewAria2Service(&cfg.Aria2)
	configService := NewConfigService(repo)
	userService := NewUserService(repo, cfg.JWT.Secret)
	downloadService := NewDownloadService(repo, aria2Service)
	taskService := NewTaskService(repo, downloadService, queueService)
	webSocketService := ws.NewService(repo.GetDB())

	// 创建文件处理服务
	processorConfig := &fileprocessor.ProcessingConfig{
		ChunkSizeMB:       cfg.FileProcessing.ChunkSizeMB,
		EncryptionEnabled: cfg.FileProcessing.EncryptionEnabled,
		KeepOriginal:      cfg.FileProcessing.KeepOriginalFiles,
		WorkDir:           cfg.FileProcessing.WorkDir,
	}

	imgbbConfig := &imgbb.Config{
		APIKey:     cfg.FileProcessing.ImgBB.APIKey,
		BaseURL:    cfg.FileProcessing.ImgBB.BaseURL,
		Timeout:    time.Duration(cfg.FileProcessing.ImgBB.Timeout) * time.Second,
		MaxRetries: cfg.FileProcessing.ImgBB.MaxRetries,
	}

	playlistConfig := &streaming.PlaylistConfig{
		Version:        cfg.FileProcessing.Playlist.Version,
		TargetDuration: cfg.FileProcessing.Playlist.TargetDuration,
		MediaSequence:  cfg.FileProcessing.Playlist.MediaSequence,
		AllowCache:     cfg.FileProcessing.Playlist.AllowCache,
		PlaylistType:   cfg.FileProcessing.Playlist.PlaylistType,
	}

	// MixFile配置
	mixFileConfig := &config.MixFileConfig{
		Enabled:                cfg.FileProcessing.MixFile.Enabled,
		EnableSteganography:    cfg.FileProcessing.MixFile.EnableSteganography,
		EnableIndexCompression: cfg.FileProcessing.MixFile.EnableIndexCompression,
		EnableIndexEncryption:  cfg.FileProcessing.MixFile.EnableIndexEncryption,
		ShareCodePrefix:        cfg.FileProcessing.MixFile.ShareCodePrefix,
		MaxShareCodeLength:     cfg.FileProcessing.MixFile.MaxShareCodeLength,
		IndexUploadRetries:     cfg.FileProcessing.MixFile.IndexUploadRetries,
	}

	// 创建DoodStream配置
	doodStreamConfig := &doodstream.Config{
		APIKey:     cfg.FileProcessing.DoodStream.APIKey,
		BaseURL:    cfg.FileProcessing.DoodStream.BaseURL,
		Timeout:    time.Duration(cfg.FileProcessing.DoodStream.Timeout) * time.Second,
		MaxRetries: cfg.FileProcessing.DoodStream.MaxRetries,
	}

	fileProcessingService := NewFileProcessingService(
		database.GetDB(),
		webSocketService,
		processorConfig,
		imgbbConfig,
		doodStreamConfig,
		cfg.FileProcessing.UploadProvider,
		playlistConfig,
		mixFileConfig,
	)

	// 设置下载服务的文件处理服务引用
	downloadService.SetFileProcessingService(fileProcessingService)

	return &Services{
		Task:           taskService,
		User:           userService,
		Config:         configService,
		Download:       downloadService,
		Queue:          queueService,
		Aria2:          aria2Service,
		WebSocket:      webSocketService,
		FileProcessing: fileProcessingService,
	}
}

// Initialize 初始化服务
func (s *Services) Initialize() error {
	// 连接aria2
	if err := s.Aria2.Connect(); err != nil {
		// aria2连接失败不应该阻止服务启动
		// logger.Warnf("Failed to connect to aria2: %v", err)
	}

	// 刷新配置缓存
	if err := s.Config.RefreshConfigCache(); err != nil {
		return err
	}

	return nil
}

// Close 关闭服务
func (s *Services) Close() error {
	// 断开aria2连接
	if err := s.Aria2.Disconnect(); err != nil {
		// logger.Warnf("Failed to disconnect from aria2: %v", err)
	}

	return nil
}

// HealthCheck 健康检查
func (s *Services) HealthCheck() map[string]bool {
	health := make(map[string]bool)

	// 检查aria2连接
	health["aria2"] = s.Aria2.Ping() == nil

	// 检查队列服务
	health["queue"] = s.Queue.HealthCheck() == nil

	return health
}
