package service

import (
	"fmt"
	"sync"

	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/pkg/logger"
)

// configService 配置服务实现
type configService struct {
	repo  repository.Repository
	cache map[string]interface{}
	mutex sync.RWMutex
}

// NewConfigService 创建配置服务
func NewConfigService(repo repository.Repository) ConfigService {
	service := &configService{
		repo:  repo,
		cache: make(map[string]interface{}),
	}

	// 初始化时加载配置到缓存
	service.loadConfigsToCache()

	return service
}

// GetConfig 获取配置
func (cs *configService) GetConfig(key string) (*model.SystemConfig, error) {
	config, err := cs.repo.Config().GetByKey(key)
	if err != nil {
		return nil, fmt.Errorf("config not found: %w", err)
	}
	return config, nil
}

// SetConfig 设置配置
func (cs *configService) SetConfig(key string, value interface{}, description string) error {
	// 验证配置
	if err := cs.ValidateConfig(key, value); err != nil {
		return fmt.Errorf("invalid config value: %w", err)
	}

	// 检查配置是否存在
	config, err := cs.repo.Config().GetByKey(key)
	if err != nil {
		// 配置不存在，创建新配置
		config = &model.SystemConfig{
			Key:         key,
			Description: description,
			Category:    cs.getCategoryByKey(key),
			IsPublic:    cs.isPublicConfig(key),
			IsEditable:  true,
		}
	}

	// 设置值
	if err := config.SetValue(value); err != nil {
		return fmt.Errorf("failed to set config value: %w", err)
	}

	// 保存到数据库
	if config.ID == 0 {
		err = cs.repo.Config().Create(config)
	} else {
		err = cs.repo.Config().Update(config)
	}

	if err != nil {
		return fmt.Errorf("failed to save config: %w", err)
	}

	// 更新缓存
	cs.updateCache(key, value)

	logger.Infof("Set config: Key=%s, Value=%v", key, value)
	return nil
}

// DeleteConfig 删除配置
func (cs *configService) DeleteConfig(key string) error {
	config, err := cs.repo.Config().GetByKey(key)
	if err != nil {
		return fmt.Errorf("config not found: %w", err)
	}

	if !config.IsEditable {
		return fmt.Errorf("config is not editable")
	}

	if err := cs.repo.Config().Delete(config.ID); err != nil {
		return fmt.Errorf("failed to delete config: %w", err)
	}

	// 从缓存中移除
	cs.removeFromCache(key)

	logger.Infof("Deleted config: Key=%s", key)
	return nil
}

// ListConfigs 获取配置列表
func (cs *configService) ListConfigs(req *ListConfigsRequest) ([]*model.SystemConfig, int64, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	offset := (req.Page - 1) * req.PageSize
	filters := cs.buildConfigFilters(req)

	configs, total, err := cs.repo.Config().List(offset, req.PageSize, filters)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list configs: %w", err)
	}

	return configs, total, nil
}

// GetConfigsByCategory 根据分类获取配置
func (cs *configService) GetConfigsByCategory(category string) ([]*model.SystemConfig, error) {
	configs, err := cs.repo.Config().GetByCategory(category)
	if err != nil {
		return nil, fmt.Errorf("failed to get configs by category: %w", err)
	}
	return configs, nil
}

// GetPublicConfigs 获取公开配置
func (cs *configService) GetPublicConfigs() ([]*model.SystemConfig, error) {
	configs, err := cs.repo.Config().GetPublicConfigs()
	if err != nil {
		return nil, fmt.Errorf("failed to get public configs: %w", err)
	}
	return configs, nil
}

// BatchSetConfigs 批量设置配置
func (cs *configService) BatchSetConfigs(configs map[string]interface{}) error {
	var configModels []*model.SystemConfig

	for key, value := range configs {
		// 验证配置
		if err := cs.ValidateConfig(key, value); err != nil {
			return fmt.Errorf("invalid config %s: %w", key, err)
		}

		// 获取或创建配置
		config, err := cs.repo.Config().GetByKey(key)
		if err != nil {
			// 配置不存在，创建新配置
			config = &model.SystemConfig{
				Key:        key,
				Category:   cs.getCategoryByKey(key),
				IsPublic:   cs.isPublicConfig(key),
				IsEditable: true,
			}
		}

		// 设置值
		if err := config.SetValue(value); err != nil {
			return fmt.Errorf("failed to set config value for %s: %w", key, err)
		}

		configModels = append(configModels, config)
	}

	// 批量保存
	if err := cs.repo.Config().BatchUpdate(configModels); err != nil {
		return fmt.Errorf("failed to batch update configs: %w", err)
	}

	// 更新缓存
	for key, value := range configs {
		cs.updateCache(key, value)
	}

	logger.Infof("Batch set %d configs", len(configs))
	return nil
}

// BatchDeleteConfigs 批量删除配置
func (cs *configService) BatchDeleteConfigs(keys []string) error {
	// 检查配置是否可编辑
	for _, key := range keys {
		config, err := cs.repo.Config().GetByKey(key)
		if err != nil {
			continue // 配置不存在，跳过
		}
		if !config.IsEditable {
			return fmt.Errorf("config %s is not editable", key)
		}
	}

	if err := cs.repo.Config().BatchDelete(keys); err != nil {
		return fmt.Errorf("failed to batch delete configs: %w", err)
	}

	// 从缓存中移除
	for _, key := range keys {
		cs.removeFromCache(key)
	}

	logger.Infof("Batch deleted %d configs", len(keys))
	return nil
}

// ValidateConfig 验证配置
func (cs *configService) ValidateConfig(key string, value interface{}) error {
	// 根据配置键进行特定验证
	switch key {
	case model.ConfigKeyMaxConcurrentDownloads:
		if v, ok := value.(int); ok {
			if v < 1 || v > 100 {
				return fmt.Errorf("max concurrent downloads must be between 1 and 100")
			}
		} else {
			return fmt.Errorf("max concurrent downloads must be an integer")
		}
	case model.ConfigKeyMaxRetries:
		if v, ok := value.(int); ok {
			if v < 0 || v > 10 {
				return fmt.Errorf("max retries must be between 0 and 10")
			}
		} else {
			return fmt.Errorf("max retries must be an integer")
		}
	case model.ConfigKeyDownloadSpeedLimit:
		if v, ok := value.(int); ok {
			if v < 0 {
				return fmt.Errorf("download speed limit cannot be negative")
			}
		} else {
			return fmt.Errorf("download speed limit must be an integer")
		}
	case model.ConfigKeyCleanAfterDays:
		if v, ok := value.(int); ok {
			if v < 1 || v > 365 {
				return fmt.Errorf("clean after days must be between 1 and 365")
			}
		} else {
			return fmt.Errorf("clean after days must be an integer")
		}
	}

	return nil
}

// RefreshConfigCache 刷新配置缓存
func (cs *configService) RefreshConfigCache() error {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	cs.cache = make(map[string]interface{})
	return cs.loadConfigsToCache()
}

// GetCachedConfig 获取缓存的配置
func (cs *configService) GetCachedConfig(key string) (interface{}, bool) {
	cs.mutex.RLock()
	defer cs.mutex.RUnlock()

	value, exists := cs.cache[key]
	return value, exists
}

// 辅助方法

// loadConfigsToCache 加载配置到缓存
func (cs *configService) loadConfigsToCache() error {
	configs, _, err := cs.repo.Config().List(0, 1000, nil)
	if err != nil {
		return err
	}

	for _, config := range configs {
		var value interface{}
		switch config.Type {
		case model.ConfigTypeString:
			value = config.GetStringValue()
		case model.ConfigTypeInt:
			value = config.GetIntValue()
		case model.ConfigTypeFloat:
			value = config.GetFloatValue()
		case model.ConfigTypeBool:
			value = config.GetBoolValue()
		case model.ConfigTypeJSON, model.ConfigTypeArray:
			var jsonValue interface{}
			if err := config.GetJSONValue(&jsonValue); err == nil {
				value = jsonValue
			} else {
				value = config.Value
			}
		default:
			value = config.Value
		}
		cs.cache[config.Key] = value
	}

	logger.Infof("Loaded %d configs to cache", len(configs))
	return nil
}

// updateCache 更新缓存
func (cs *configService) updateCache(key string, value interface{}) {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()
	cs.cache[key] = value
}

// removeFromCache 从缓存中移除
func (cs *configService) removeFromCache(key string) {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()
	delete(cs.cache, key)
}

// getCategoryByKey 根据键获取分类
func (cs *configService) getCategoryByKey(key string) string {
	switch key {
	case model.ConfigKeyMaxConcurrentDownloads,
		model.ConfigKeyDefaultDownloadPath,
		model.ConfigKeyMaxRetries,
		model.ConfigKeyDownloadSpeedLimit,
		model.ConfigKeyUploadSpeedLimit:
		return "download"
	case model.ConfigKeyAutoCleanCompleted,
		model.ConfigKeyCleanAfterDays,
		model.ConfigKeyEnableNotifications,
		model.ConfigKeySystemMaintenance:
		return "system"
	default:
		return "general"
	}
}

// isPublicConfig 检查是否为公开配置
func (cs *configService) isPublicConfig(key string) bool {
	switch key {
	case model.ConfigKeySystemMaintenance:
		return false
	default:
		return true
	}
}

// buildConfigFilters 构建配置查询过滤器
func (cs *configService) buildConfigFilters(req *ListConfigsRequest) map[string]interface{} {
	filters := make(map[string]interface{})

	if req.Category != "" {
		filters["category"] = req.Category
	}
	if req.IsPublic != nil {
		filters["is_public"] = *req.IsPublic
	}
	if req.Search != "" {
		filters["search"] = req.Search
	}

	// 添加自定义过滤器
	for key, value := range req.Filters {
		filters[key] = value
	}

	return filters
}
