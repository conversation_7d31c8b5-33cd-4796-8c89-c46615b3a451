package service

import (
	"magnet-downloader/internal/model"
)

// TaskService 任务服务接口
type TaskService interface {
	// 任务CRUD操作
	CreateTask(userID uint, req *CreateTaskRequest) (*model.DownloadTask, error)
	GetTask(taskID uint) (*model.DownloadTask, error)
	UpdateTask(taskID uint, req *UpdateTaskRequest) (*model.DownloadTask, error)
	DeleteTask(taskID uint) error

	// 任务查询
	ListTasks(req *ListTasksRequest) ([]*model.DownloadTask, int64, error)
	GetUserTasks(userID uint, req *ListTasksRequest) ([]*model.DownloadTask, int64, error)
	GetTasksByStatus(status model.TaskStatus, req *ListTasksRequest) ([]*model.DownloadTask, int64, error)

	// 任务控制
	StartTask(taskID uint) error
	PauseTask(taskID uint) error
	ResumeTask(taskID uint) error
	CancelTask(taskID uint) error
	RetryTask(taskID uint) error

	// 批量操作
	BatchStart(taskIDs []uint) (*BatchOperationResult, error)
	BatchPause(taskIDs []uint) (*BatchOperationResult, error)
	BatchResume(taskIDs []uint) (*BatchOperationResult, error)
	BatchCancel(taskIDs []uint) (*BatchOperationResult, error)
	BatchDelete(taskIDs []uint) (*BatchOperationResult, error)

	// 任务统计
	GetTaskStats() (*TaskStats, error)
	GetUserTaskStats(userID uint) (*UserTaskStats, error)
}

// UserService 用户服务接口
type UserService interface {
	// 用户认证
	Login(req *LoginRequest) (*LoginResponse, error)
	Logout(userID uint) error
	RefreshToken(refreshToken string) (*TokenResponse, error)

	// 用户CRUD操作
	CreateUser(req *CreateUserRequest) (*model.User, error)
	GetUser(userID uint) (*model.User, error)
	UpdateUser(userID uint, req *UpdateUserRequest) (*model.User, error)
	DeleteUser(userID uint) error

	// 用户查询
	ListUsers(req *ListUsersRequest) ([]*model.User, int64, error)
	GetUserByUsername(username string) (*model.User, error)
	GetUserByEmail(email string) (*model.User, error)

	// 密码管理
	ChangePassword(userID uint, req *ChangePasswordRequest) error
	ResetPassword(email string) error

	// 用户权限
	CheckPermission(userID uint, permission string) (bool, error)
	UpdateUserRole(userID uint, role model.UserRole) error
	UpdateUserStatus(userID uint, status model.UserStatus) error

	// 用户统计
	GetUserStats() (*UserStats, error)
}

// ConfigService 配置服务接口
type ConfigService interface {
	// 配置CRUD操作
	GetConfig(key string) (*model.SystemConfig, error)
	SetConfig(key string, value interface{}, description string) error
	DeleteConfig(key string) error

	// 配置查询
	ListConfigs(req *ListConfigsRequest) ([]*model.SystemConfig, int64, error)
	GetConfigsByCategory(category string) ([]*model.SystemConfig, error)
	GetPublicConfigs() ([]*model.SystemConfig, error)

	// 批量操作
	BatchSetConfigs(configs map[string]interface{}) error
	BatchDeleteConfigs(keys []string) error

	// 配置验证
	ValidateConfig(key string, value interface{}) error

	// 配置缓存
	RefreshConfigCache() error
	GetCachedConfig(key string) (interface{}, bool)
}

// FileProcessingService 文件处理服务接口
type FileProcessingService interface {
	// 处理控制
	StartProcessing(taskID uint) error
	PauseProcessing(taskID uint) error
	ResumeProcessing(taskID uint) error
	CancelProcessing(taskID uint) error
	RetryProcessing(taskID uint) error

	// 处理状态查询
	GetProcessingStatus(taskID uint) (*ProcessingStatusResponse, error)
	GetProcessingProgress(taskID uint) (*ProcessingProgressResponse, error)
	ListProcessingTasks(req *ListProcessingTasksRequest) ([]*model.DownloadTask, int64, error)

	// 播放列表管理
	GetPlaylist(taskID uint) (*PlaylistResponse, error)
	GeneratePlaylist(taskID uint) (*PlaylistResponse, error)
	RefreshPlaylist(taskID uint) (*PlaylistResponse, error)

	// 批量操作
	BatchStartProcessing(taskIDs []uint) (*BatchOperationResult, error)
	BatchCancelProcessing(taskIDs []uint) (*BatchOperationResult, error)

	// 处理统计
	GetProcessingStats() (*ProcessingStats, error)
	GetUserProcessingStats(userID uint) (*UserProcessingStats, error)

	// 清理操作
	CleanupProcessingFiles(taskID uint) error
	CleanupExpiredFiles() error
}

// 请求和响应结构体

// CreateTaskRequest 创建任务请求
type CreateTaskRequest struct {
	MagnetURI string                 `json:"magnet_uri" binding:"required"`
	TaskName  string                 `json:"task_name"`
	SavePath  string                 `json:"save_path"`
	Priority  model.TaskPriority     `json:"priority"`
	Options   map[string]interface{} `json:"options"`
}

// UpdateTaskRequest 更新任务请求
type UpdateTaskRequest struct {
	TaskName string             `json:"task_name"`
	Priority model.TaskPriority `json:"priority"`
	SavePath string             `json:"save_path"`
}

// ListTasksRequest 任务列表请求
type ListTasksRequest struct {
	Page     int                    `json:"page" form:"page"`
	PageSize int                    `json:"page_size" form:"page_size"`
	Status   model.TaskStatus       `json:"status" form:"status"`
	Priority model.TaskPriority     `json:"priority" form:"priority"`
	Search   string                 `json:"search" form:"search"`
	SortBy   string                 `json:"sort_by" form:"sort_by"`
	SortDesc bool                   `json:"sort_desc" form:"sort_desc"`
	Filters  map[string]interface{} `json:"filters"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	User         *model.UserProfile `json:"user"`
	AccessToken  string             `json:"access_token"`
	RefreshToken string             `json:"refresh_token"`
	ExpiresIn    int64              `json:"expires_in"`
}

// TokenResponse 令牌响应
type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string           `json:"username" binding:"required,min=3,max=50"`
	Email    string           `json:"email" binding:"required,email"`
	Password string           `json:"password" binding:"required,min=6"`
	Role     model.UserRole   `json:"role"`
	Status   model.UserStatus `json:"status"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Email  string           `json:"email" binding:"omitempty,email"`
	Role   model.UserRole   `json:"role"`
	Status model.UserStatus `json:"status"`
	Avatar string           `json:"avatar"`
}

// ListUsersRequest 用户列表请求
type ListUsersRequest struct {
	Page     int                    `json:"page" form:"page"`
	PageSize int                    `json:"page_size" form:"page_size"`
	Role     model.UserRole         `json:"role" form:"role"`
	Status   model.UserStatus       `json:"status" form:"status"`
	Search   string                 `json:"search" form:"search"`
	SortBy   string                 `json:"sort_by" form:"sort_by"`
	SortDesc bool                   `json:"sort_desc" form:"sort_desc"`
	Filters  map[string]interface{} `json:"filters"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

// ListConfigsRequest 配置列表请求
type ListConfigsRequest struct {
	Page     int                    `json:"page" form:"page"`
	PageSize int                    `json:"page_size" form:"page_size"`
	Category string                 `json:"category" form:"category"`
	IsPublic *bool                  `json:"is_public" form:"is_public"`
	Search   string                 `json:"search" form:"search"`
	SortBy   string                 `json:"sort_by" form:"sort_by"`
	SortDesc bool                   `json:"sort_desc" form:"sort_desc"`
	Filters  map[string]interface{} `json:"filters"`
}

// 批量操作结果
type BatchOperationResult struct {
	Total      int      `json:"total"`
	Success    int      `json:"success"`
	Failed     int      `json:"failed"`
	Errors     []string `json:"errors,omitempty"`
	SuccessIDs []uint   `json:"success_ids,omitempty"`
	FailedIDs  []uint   `json:"failed_ids,omitempty"`
}

// 统计信息结构体
type TaskStats struct {
	TotalTasks     int64   `json:"total_tasks"`
	ActiveTasks    int64   `json:"active_tasks"`
	PendingTasks   int64   `json:"pending_tasks"`
	CompletedTasks int64   `json:"completed_tasks"`
	FailedTasks    int64   `json:"failed_tasks"`
	PausedTasks    int64   `json:"paused_tasks"`
	CancelledTasks int64   `json:"cancelled_tasks"`
	TotalSize      int64   `json:"total_size"`
	DownloadedSize int64   `json:"downloaded_size"`
	DownloadSpeed  int64   `json:"download_speed"`
	UploadSpeed    int64   `json:"upload_speed"`
	SuccessRate    float64 `json:"success_rate"`
}

// UserTaskStats 用户任务统计
type UserTaskStats struct {
	UserID         uint    `json:"user_id"`
	TotalTasks     int64   `json:"total_tasks"`
	ActiveTasks    int64   `json:"active_tasks"`
	CompletedTasks int64   `json:"completed_tasks"`
	FailedTasks    int64   `json:"failed_tasks"`
	TotalSize      int64   `json:"total_size"`
	DownloadedSize int64   `json:"downloaded_size"`
	SuccessRate    float64 `json:"success_rate"`
}

// UserStats 用户统计
type UserStats struct {
	TotalUsers    int64 `json:"total_users"`
	ActiveUsers   int64 `json:"active_users"`
	AdminUsers    int64 `json:"admin_users"`
	BannedUsers   int64 `json:"banned_users"`
	NewUsers24h   int64 `json:"new_users_24h"`
	LoginUsers24h int64 `json:"login_users_24h"`
}

// 文件处理相关结构体

// ProcessingStatusResponse 处理状态响应
type ProcessingStatusResponse struct {
	TaskID             uint    `json:"task_id"`
	Status             string  `json:"status"`
	ProcessingStatus   string  `json:"processing_status"`
	ProcessingProgress float64 `json:"processing_progress"`
	ChunkCount         int     `json:"chunk_count"`
	UploadedChunks     int     `json:"uploaded_chunks"`
	ErrorMessage       string  `json:"error_message,omitempty"`
	StartedAt          *int64  `json:"started_at,omitempty"`
	CompletedAt        *int64  `json:"completed_at,omitempty"`
}

// ProcessingProgressResponse 处理进度响应
type ProcessingProgressResponse struct {
	TaskID             uint    `json:"task_id"`
	Stage              string  `json:"stage"`
	Progress           float64 `json:"progress"`
	OverallProgress    float64 `json:"overall_progress"`
	ChunkCount         int     `json:"chunk_count"`
	ProcessedChunks    int     `json:"processed_chunks"`
	UploadedChunks     int     `json:"uploaded_chunks"`
	CurrentChunk       int     `json:"current_chunk"`
	Message            string  `json:"message"`
	EstimatedRemaining int64   `json:"estimated_remaining"`
}

// ListProcessingTasksRequest 处理任务列表请求
type ListProcessingTasksRequest struct {
	Page             int                    `json:"page" form:"page"`
	PageSize         int                    `json:"page_size" form:"page_size"`
	ProcessingStatus string                 `json:"processing_status" form:"processing_status"`
	Search           string                 `json:"search" form:"search"`
	SortBy           string                 `json:"sort_by" form:"sort_by"`
	SortDesc         bool                   `json:"sort_desc" form:"sort_desc"`
	Filters          map[string]interface{} `json:"filters"`
}

// PlaylistResponse 播放列表响应
type PlaylistResponse struct {
	TaskID      uint    `json:"task_id"`
	PlaylistURL string  `json:"playlist_url"`
	Content     string  `json:"content"`
	ChunkCount  int     `json:"chunk_count"`
	Duration    float64 `json:"duration"`
	Encrypted   bool    `json:"encrypted"`
	CreatedAt   int64   `json:"created_at"`
	UpdatedAt   int64   `json:"updated_at"`
}

// ProcessingStats 处理统计
type ProcessingStats struct {
	TotalProcessingTasks int64   `json:"total_processing_tasks"`
	ActiveProcessing     int64   `json:"active_processing"`
	CompletedProcessing  int64   `json:"completed_processing"`
	FailedProcessing     int64   `json:"failed_processing"`
	TotalChunks          int64   `json:"total_chunks"`
	UploadedChunks       int64   `json:"uploaded_chunks"`
	TotalPlaylistsReady  int64   `json:"total_playlists_ready"`
	AvgProcessingTime    float64 `json:"avg_processing_time"`
	SuccessRate          float64 `json:"success_rate"`
}

// UserProcessingStats 用户处理统计
type UserProcessingStats struct {
	UserID              uint    `json:"user_id"`
	TotalProcessing     int64   `json:"total_processing"`
	CompletedProcessing int64   `json:"completed_processing"`
	FailedProcessing    int64   `json:"failed_processing"`
	TotalChunks         int64   `json:"total_chunks"`
	UploadedChunks      int64   `json:"uploaded_chunks"`
	PlaylistsReady      int64   `json:"playlists_ready"`
	SuccessRate         float64 `json:"success_rate"`
}
