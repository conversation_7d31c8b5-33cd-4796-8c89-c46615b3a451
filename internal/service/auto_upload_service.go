package service

import (
	"context"
	"fmt"
	"os"
	"sync"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/model"
	"magnet-downloader/internal/websocket"
	"magnet-downloader/pkg/doodstream"
	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/logger"

	"gorm.io/gorm"
)

// AutoUploadService 自动上传服务接口
type AutoUploadService interface {
	// 启动自动上传服务
	Start(ctx context.Context) error
	// 停止自动上传服务
	Stop() error
	// 手动触发扫描和上传
	TriggerScan() error
	// 获取服务状态
	GetStatus() *AutoUploadStatus
	// 获取上传统计
	GetStats() *AutoUploadStats
}

// AutoUploadStatus 自动上传服务状态
type AutoUploadStatus struct {
	Running        bool      `json:"running"`
	LastScanTime   time.Time `json:"last_scan_time"`
	NextScanTime   time.Time `json:"next_scan_time"`
	ScanInterval   int       `json:"scan_interval_minutes"`
	PendingUploads int       `json:"pending_uploads"`
	ActiveUploads  int       `json:"active_uploads"`
}

// AutoUploadStats 自动上传统计
type AutoUploadStats struct {
	TotalScans       int64     `json:"total_scans"`
	TotalUploads     int64     `json:"total_uploads"`
	SuccessfulUploads int64    `json:"successful_uploads"`
	FailedUploads    int64     `json:"failed_uploads"`
	TotalDataUploaded int64    `json:"total_data_uploaded"`
	LastUploadTime   time.Time `json:"last_upload_time"`
	UploadSuccessRate float64  `json:"upload_success_rate"`
}

// UploadTask 上传任务
type UploadTask struct {
	ID           string                     `json:"id"`
	VideoFile    fileprocessor.VideoFile    `json:"video_file"`
	Status       string                     `json:"status"`
	Progress     float64                    `json:"progress"`
	StartTime    time.Time                  `json:"start_time"`
	EndTime      time.Time                  `json:"end_time"`
	Error        string                     `json:"error,omitempty"`
	Result       *doodstream.UploadResult   `json:"result,omitempty"`
}

// autoUploadService 自动上传服务实现
type autoUploadService struct {
	db               *gorm.DB
	websocketSvc     *websocket.Service
	doodStreamClient *doodstream.Client
	videoSelector    *fileprocessor.VideoSelector
	config           *config.Config
	
	// 服务状态
	running          bool
	ctx              context.Context
	cancel           context.CancelFunc
	mutex            sync.RWMutex
	
	// 配置参数
	downloadDir      string
	scanInterval     time.Duration
	maxConcurrentUploads int
	
	// 运行时状态
	status           *AutoUploadStatus
	stats            *AutoUploadStats
	pendingTasks     []UploadTask
	activeTasks      map[string]*UploadTask
	taskMutex        sync.RWMutex
	
	// 上传控制
	uploadSemaphore  chan struct{}
}

// NewAutoUploadService 创建自动上传服务
func NewAutoUploadService(
	db *gorm.DB,
	websocketSvc *websocket.Service,
	doodStreamClient *doodstream.Client,
	cfg *config.Config,
) AutoUploadService {
	
	// 确定下载目录
	downloadDir := "/downloads" // 默认下载目录
	if envDir := os.Getenv("DOWNLOAD_DIR"); envDir != "" {
		downloadDir = envDir
	}

	service := &autoUploadService{
		db:               db,
		websocketSvc:     websocketSvc,
		doodStreamClient: doodStreamClient,
		videoSelector:    fileprocessor.NewVideoSelector(),
		config:           cfg,
		
		downloadDir:      downloadDir,
		scanInterval:     time.Duration(cfg.FileProcessing.AutoUpload.ScanInterval) * time.Minute,
		maxConcurrentUploads: cfg.FileProcessing.AutoUpload.MaxConcurrentUploads,
		
		status: &AutoUploadStatus{
			Running:      false,
			ScanInterval: cfg.FileProcessing.AutoUpload.ScanInterval,
		},
		stats: &AutoUploadStats{},
		activeTasks: make(map[string]*UploadTask),
	}
	
	// 创建上传信号量
	service.uploadSemaphore = make(chan struct{}, service.maxConcurrentUploads)
	
	return service
}

// Start 启动自动上传服务
func (s *autoUploadService) Start(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	if s.running {
		return fmt.Errorf("自动上传服务已在运行")
	}
	
	s.ctx, s.cancel = context.WithCancel(ctx)
	s.running = true
	s.status.Running = true
	
	logger.Info("启动自动上传服务")
	
	// 启动扫描循环
	go s.scanLoop()
	
	return nil
}

// Stop 停止自动上传服务
func (s *autoUploadService) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	if !s.running {
		return fmt.Errorf("自动上传服务未在运行")
	}
	
	logger.Info("停止自动上传服务")
	
	s.cancel()
	s.running = false
	s.status.Running = false
	
	// 等待所有活跃上传完成
	s.waitForActiveUploads()
	
	return nil
}

// TriggerScan 手动触发扫描
func (s *autoUploadService) TriggerScan() error {
	if !s.running {
		return fmt.Errorf("自动上传服务未在运行")
	}
	
	logger.Info("手动触发视频扫描")
	go s.performScan()
	
	return nil
}

// GetStatus 获取服务状态
func (s *autoUploadService) GetStatus() *AutoUploadStatus {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	status := *s.status
	status.PendingUploads = len(s.pendingTasks)
	status.ActiveUploads = len(s.activeTasks)
	
	if s.running && s.scanInterval > 0 {
		status.NextScanTime = status.LastScanTime.Add(s.scanInterval)
	}
	
	return &status
}

// GetStats 获取上传统计
func (s *autoUploadService) GetStats() *AutoUploadStats {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	stats := *s.stats
	
	// 计算成功率
	if stats.TotalUploads > 0 {
		stats.UploadSuccessRate = float64(stats.SuccessfulUploads) / float64(stats.TotalUploads) * 100
	}
	
	return &stats
}

// scanLoop 扫描循环
func (s *autoUploadService) scanLoop() {
	ticker := time.NewTicker(s.scanInterval)
	defer ticker.Stop()
	
	// 启动时立即执行一次扫描
	s.performScan()
	
	for {
		select {
		case <-s.ctx.Done():
			logger.Info("自动上传服务扫描循环退出")
			return
		case <-ticker.C:
			s.performScan()
		}
	}
}

// performScan 执行扫描
func (s *autoUploadService) performScan() {
	logger.Info("开始扫描下载目录寻找新视频")
	
	s.mutex.Lock()
	s.status.LastScanTime = time.Now()
	s.stats.TotalScans++
	s.mutex.Unlock()
	
	// 扫描视频文件
	videos, err := s.videoSelector.SelectVideosFromDirectory(s.downloadDir)
	if err != nil {
		logger.Errorf("扫描视频文件失败: %v", err)
		return
	}
	
	if len(videos) == 0 {
		logger.Info("没有找到需要上传的新视频")
		return
	}
	
	logger.Infof("找到 %d 个视频文件需要上传", len(videos))
	
	// 过滤已上传的视频
	newVideos := s.filterNewVideos(videos)
	if len(newVideos) == 0 {
		logger.Info("所有视频都已上传")
		return
	}
	
	logger.Infof("发现 %d 个新视频需要上传", len(newVideos))
	
	// 创建上传任务
	s.createUploadTasks(newVideos)
	
	// 启动上传处理
	s.processUploadTasks()
}

// filterNewVideos 过滤已上传的视频
func (s *autoUploadService) filterNewVideos(videos []fileprocessor.VideoFile) []fileprocessor.VideoFile {
	var newVideos []fileprocessor.VideoFile
	
	for _, video := range videos {
		// 检查数据库中是否已存在该视频的上传记录
		var count int64
		s.db.Model(&model.DownloadTask{}).
			Where("save_path = ? AND play_url IS NOT NULL AND play_url != ''", video.Path).
			Count(&count)
		
		if count == 0 {
			newVideos = append(newVideos, video)
		} else {
			logger.Debugf("视频已上传，跳过: %s", video.Path)
		}
	}
	
	return newVideos
}

// createUploadTasks 创建上传任务
func (s *autoUploadService) createUploadTasks(videos []fileprocessor.VideoFile) {
	s.taskMutex.Lock()
	defer s.taskMutex.Unlock()
	
	for _, video := range videos {
		task := UploadTask{
			ID:        fmt.Sprintf("upload_%d", time.Now().UnixNano()),
			VideoFile: video,
			Status:    "pending",
			Progress:  0,
		}
		
		s.pendingTasks = append(s.pendingTasks, task)
		logger.Infof("创建上传任务: %s -> %s", task.ID, video.Name)
	}
}

// processUploadTasks 处理上传任务
func (s *autoUploadService) processUploadTasks() {
	s.taskMutex.RLock()
	pendingCount := len(s.pendingTasks)
	s.taskMutex.RUnlock()
	
	if pendingCount == 0 {
		return
	}
	
	logger.Infof("开始处理 %d 个上传任务", pendingCount)
	
	// 启动上传工作协程
	for i := 0; i < s.maxConcurrentUploads; i++ {
		go s.uploadWorker()
	}
}

// uploadWorker 上传工作协程
func (s *autoUploadService) uploadWorker() {
	for {
		// 获取下一个待上传任务
		task := s.getNextPendingTask()
		if task == nil {
			// 没有更多任务，退出
			return
		}
		
		// 获取上传信号量
		select {
		case s.uploadSemaphore <- struct{}{}:
			// 执行上传
			s.executeUpload(task)
			// 释放信号量
			<-s.uploadSemaphore
		case <-s.ctx.Done():
			// 服务被停止
			return
		}
	}
}

// getNextPendingTask 获取下一个待上传任务
func (s *autoUploadService) getNextPendingTask() *UploadTask {
	s.taskMutex.Lock()
	defer s.taskMutex.Unlock()
	
	if len(s.pendingTasks) == 0 {
		return nil
	}
	
	// 取出第一个任务
	task := s.pendingTasks[0]
	s.pendingTasks = s.pendingTasks[1:]
	
	// 移动到活跃任务列表
	s.activeTasks[task.ID] = &task
	
	return &task
}

// executeUpload 执行上传
func (s *autoUploadService) executeUpload(task *UploadTask) {
	logger.Infof("开始上传视频: %s", task.VideoFile.Name)
	
	// 更新任务状态
	task.Status = "uploading"
	task.StartTime = time.Now()
	task.Progress = 0
	
	// 发送WebSocket通知
	s.notifyUploadStart(task)
	
	// 验证视频文件
	if err := s.videoSelector.ValidateVideoFile(task.VideoFile.Path); err != nil {
		s.handleUploadError(task, fmt.Errorf("视频文件验证失败: %w", err))
		return
	}
	
	// 执行上传
	result, err := s.doodStreamClient.UploadFile(task.VideoFile.Path)
	if err != nil {
		s.handleUploadError(task, err)
		return
	}
	
	// 上传成功
	s.handleUploadSuccess(task, result)
}

// handleUploadSuccess 处理上传成功
func (s *autoUploadService) handleUploadSuccess(task *UploadTask, result *doodstream.UploadResult) {
	task.Status = "completed"
	task.Progress = 100
	task.EndTime = time.Now()
	task.Result = result
	
	logger.Infof("视频上传成功: %s -> %s", task.VideoFile.Name, result.PlayURL)
	
	// 更新统计
	s.mutex.Lock()
	s.stats.TotalUploads++
	s.stats.SuccessfulUploads++
	s.stats.TotalDataUploaded += task.VideoFile.Size
	s.stats.LastUploadTime = time.Now()
	s.mutex.Unlock()
	
	// 保存到数据库
	s.saveUploadResult(task, result)
	
	// 发送WebSocket通知
	s.notifyUploadComplete(task)
	
	// 从活跃任务中移除
	s.removeActiveTask(task.ID)
}

// handleUploadError 处理上传错误
func (s *autoUploadService) handleUploadError(task *UploadTask, err error) {
	task.Status = "failed"
	task.EndTime = time.Now()
	task.Error = err.Error()
	
	logger.Errorf("视频上传失败: %s -> %v", task.VideoFile.Name, err)
	
	// 更新统计
	s.mutex.Lock()
	s.stats.TotalUploads++
	s.stats.FailedUploads++
	s.mutex.Unlock()
	
	// 发送WebSocket通知
	s.notifyUploadError(task)
	
	// 从活跃任务中移除
	s.removeActiveTask(task.ID)
}

// saveUploadResult 保存上传结果到数据库
func (s *autoUploadService) saveUploadResult(task *UploadTask, result *doodstream.UploadResult) {
	// 查找或创建下载任务记录
	var downloadTask model.DownloadTask
	
	// 尝试根据文件路径查找现有任务
	err := s.db.Where("save_path = ?", task.VideoFile.Path).First(&downloadTask).Error
	if err == gorm.ErrRecordNotFound {
		// 创建新的任务记录
		downloadTask = model.DownloadTask{
			TaskName:         task.VideoFile.Name,
			SavePath:         task.VideoFile.Path,
			TotalSize:        task.VideoFile.Size,
			Status:           model.TaskStatusCompleted,
			ProcessingStatus: "completed",
			PlayURL:          result.PlayURL,
			ShareCode:        result.FileCode,
			CompletedAt:      &task.EndTime,
			UserID:          1, // 默认用户ID，实际应用中应该从上下文获取
		}
		
		if err := s.db.Create(&downloadTask).Error; err != nil {
			logger.Errorf("创建下载任务记录失败: %v", err)
		}
	} else if err == nil {
		// 更新现有任务记录
		updates := map[string]interface{}{
			"play_url":          result.PlayURL,
			"share_code":        result.FileCode,
			"total_size":        task.VideoFile.Size,
			"processing_status": "completed",
			"status":            model.TaskStatusCompleted,
			"completed_at":      task.EndTime,
		}
		
		if err := s.db.Model(&downloadTask).Updates(updates).Error; err != nil {
			logger.Errorf("更新下载任务记录失败: %v", err)
		}
	} else {
		logger.Errorf("查询下载任务记录失败: %v", err)
	}
}

// removeActiveTask 从活跃任务中移除
func (s *autoUploadService) removeActiveTask(taskID string) {
	s.taskMutex.Lock()
	defer s.taskMutex.Unlock()
	
	delete(s.activeTasks, taskID)
}

// waitForActiveUploads 等待所有活跃上传完成
func (s *autoUploadService) waitForActiveUploads() {
	logger.Info("等待活跃上传任务完成...")
	
	for {
		s.taskMutex.RLock()
		activeCount := len(s.activeTasks)
		s.taskMutex.RUnlock()
		
		if activeCount == 0 {
			break
		}
		
		logger.Infof("还有 %d 个活跃上传任务，等待完成...", activeCount)
		time.Sleep(2 * time.Second)
	}
	
	logger.Info("所有活跃上传任务已完成")
}

// WebSocket通知方法
func (s *autoUploadService) notifyUploadStart(task *UploadTask) {
	if s.websocketSvc != nil {
		s.websocketSvc.BroadcastSystemNotification(
			"自动上传开始",
			fmt.Sprintf("开始上传视频: %s", task.VideoFile.Name),
			"info",
		)
	}
}

func (s *autoUploadService) notifyUploadComplete(task *UploadTask) {
	if s.websocketSvc != nil {
		s.websocketSvc.BroadcastSystemNotification(
			"自动上传完成",
			fmt.Sprintf("视频 %s 上传成功", task.VideoFile.Name),
			"success",
		)
	}
}

func (s *autoUploadService) notifyUploadError(task *UploadTask) {
	if s.websocketSvc != nil {
		s.websocketSvc.BroadcastSystemNotification(
			"自动上传失败",
			fmt.Sprintf("视频 %s 上传失败: %s", task.VideoFile.Name, task.Error),
			"error",
		)
	}
}