# 磁力下载器 (Magnet Downloader)

[![Go Version](https://img.shields.io/badge/Go-1.21+-blue.svg)](https://golang.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com)
[![Kubernetes](https://img.shields.io/badge/Kubernetes-Ready-blue.svg)](https://kubernetes.io)

一个基于Go语言开发的高性能磁力链接下载管理系统，集成aria2下载引擎，提供Web管理界面和RESTful API。

## ✨ 特性

### 🚀 核心功能
- **磁力链接下载**: 支持磁力链接和种子文件下载
- **任务管理**: 创建、暂停、恢复、取消下载任务
- **进度监控**: 实时显示下载进度和速度
- **用户管理**: 多用户支持，权限控制
- **Web界面**: 现代化的React前端管理界面

### 📦 文件处理系统
- **自动分片处理**: 1MB文件分片，优化传输和存储
- **端到端加密**: AES-GCM 256-bit加密保护文件安全
- **并发上传**: 支持imgbb图床并发上传，提高效率
- **播放列表生成**: 标准HLS m3u8格式，兼容主流播放器
- **实时进度监控**: WebSocket实时推送处理进度
- **智能重试机制**: 指数退避算法，确保上传成功率

### 🔧 技术特性
- **高性能**: 基于Go语言和aria2引擎
- **实时通信**: WebSocket实时推送任务状态
- **任务调度**: 支持定时任务和批量处理
- **监控告警**: Prometheus指标收集和Grafana可视化
- **容器化**: Docker和Kubernetes部署支持

### 🛡️ 安全特性
- **JWT认证**: 安全的用户认证机制
- **权限控制**: 基于角色的访问控制
- **数据加密**: 敏感数据加密存储
- **限流保护**: API请求限流和防护

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React前端     │    │   Nginx代理     │    │   Go后端API     │
│                 │◄──►│                 │◄──►│                 │
│ - 任务管理      │    │ - 负载均衡      │    │ - RESTful API   │
│ - 用户界面      │    │ - 静态文件      │    │ - WebSocket     │
│ - 实时监控      │    │ - SSL终端       │    │ - 业务逻辑      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐             │
                       │   Aria2引擎     │◄────────────┤
                       │                 │             │
                       │ - 下载管理      │             │
                       │ - 进度监控      │             │
                       │ - 速度控制      │             │
                       └─────────────────┘             │
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  PostgreSQL     │    │     Redis       │    │   监控系统      │
│                 │◄──►│                 │◄──►│                 │
│ - 数据存储      │    │ - 缓存          │    │ - Prometheus    │
│ - 事务处理      │    │ - 会话管理      │    │ - Grafana       │
│ - 数据备份      │    │ - 任务队列      │    │ - AlertManager  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- Go 1.21+
- Docker & Docker Compose
- PostgreSQL 12+
- Redis 6+
- Node.js 16+ (前端开发)

### 使用Docker Compose部署（推荐）

1. **克隆项目**
```bash
git clone https://github.com/your-username/magnet-downloader.git
cd magnet-downloader
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，修改数据库密码等配置
```

3. **启动服务**
```bash
# 生产环境
./scripts/deploy.sh prod up

# 开发环境
./scripts/deploy.sh dev up
```

4. **访问应用**
- Web界面: http://localhost
- API文档: http://localhost/api/docs
- 监控面板: http://localhost:3000 (Grafana)

### 手动部署

<details>
<summary>点击展开手动部署步骤</summary>

1. **安装依赖**
```bash
# 安装Go依赖
go mod download

# 安装前端依赖
cd web && npm install
```

2. **配置数据库**
```bash
# 创建数据库
createdb magnet_downloader

# 运行迁移
go run cmd/migrate/main.go
```

3. **构建应用**
```bash
# 构建后端
go build -o bin/magnet-downloader cmd/server/main.go

# 构建前端
cd web && npm run build
```

4. **启动服务**
```bash
# 启动后端
./bin/magnet-downloader

# 启动aria2
aria2c --enable-rpc --rpc-listen-all --rpc-secret=your-secret
```

</details>

## 📖 使用指南

### 基本操作

1. **登录系统**
   - 默认管理员: `admin` / `admin123`
   - 默认用户: `user` / `user123`

2. **添加下载任务**
   - 复制磁力链接
   - 在任务管理页面点击"新建任务"
   - 粘贴磁力链接并设置下载参数

3. **管理任务**
   - 查看下载进度和速度
   - 暂停/恢复/取消任务
   - 设置任务优先级

### API使用

```bash
# 获取任务列表
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/v1/tasks

# 创建下载任务
curl -X POST \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"magnet_uri":"magnet:?xt=urn:btih:...","task_name":"示例任务"}' \
     http://localhost:8080/api/v1/tasks

# 获取任务详情
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/v1/tasks/1
```

## 🔧 配置说明

### 主要配置文件

- `config.yaml` - 主配置文件
- `.env` - 环境变量配置
- `docker-compose.yml` - Docker编排配置

### 重要配置项

```yaml
# 应用配置
app:
  name: "磁力下载器"
  version: "1.0.0"
  env: "production"

# 数据库配置
database:
  host: "localhost"
  port: 5432
  name: "magnet_downloader"
  user: "postgres"
  password: "your-password"

# Aria2配置
aria2:
  host: "localhost"
  port: 6800
  secret: "your-secret"
  max_concurrent_downloads: 5

# 文件处理配置
file_processing:
  enabled: true                    # 是否启用文件处理
  chunk_size_mb: 1                 # 分片大小(MB)
  max_concurrent_uploads: 3        # 最大并发上传数
  encryption_enabled: true         # 是否启用加密
  auto_start_processing: true      # 下载完成后自动开始处理

  # ImgBB图床配置
  imgbb:
    api_key: "your-imgbb-api-key"  # ImgBB API密钥
    timeout: 30                    # 请求超时时间(秒)
    max_retries: 3                 # 最大重试次数
```

## 📊 监控和运维

### 监控指标

系统提供丰富的监控指标：

- **应用指标**: HTTP请求、响应时间、错误率
- **业务指标**: 任务数量、下载速度、成功率
- **系统指标**: CPU、内存、磁盘使用率
- **数据库指标**: 连接数、查询性能

### 日志管理

```bash
# 查看应用日志
docker-compose logs -f magnet-downloader

# 查看特定服务日志
docker-compose logs -f postgres redis aria2
```

### 数据备份

```bash
# 自动备份
./scripts/deploy.sh prod backup

# 手动备份数据库
docker-compose exec postgres pg_dump -U postgres magnet_downloader > backup.sql
```

## 🛠️ 开发指南

### 开发环境搭建

```bash
# 启动开发环境
./scripts/deploy.sh dev up

# 热重载开发
cd web && npm start  # 前端热重载
air                  # 后端热重载
```

### 项目结构

```
magnet-downloader/
├── cmd/                    # 应用入口
├── internal/              # 内部包
│   ├── api/               # API层
│   ├── service/           # 业务逻辑层
│   ├── repository/        # 数据访问层
│   └── model/             # 数据模型
├── pkg/                   # 公共包
├── web/                   # 前端代码
├── deployments/           # 部署配置
├── scripts/               # 脚本文件
└── docs/                  # 文档
```

### 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📝 API文档

详细的API文档请访问: [API Documentation](docs/api.md)

主要API端点：

**认证相关**
- `POST /api/v1/auth/login` - 用户登录

**任务管理**
- `GET /api/v1/tasks` - 获取任务列表
- `POST /api/v1/tasks` - 创建下载任务
- `PUT /api/v1/tasks/:id` - 更新任务
- `DELETE /api/v1/tasks/:id` - 删除任务

**文件处理**
- `POST /api/v1/processing/:id/start` - 开始文件处理
- `GET /api/v1/processing/:id/status` - 获取处理状态
- `GET /api/v1/processing/:id/progress` - 获取处理进度
- `GET /api/v1/processing/:id/playlist` - 获取播放列表
- `GET /api/v1/processing/stats` - 获取处理统计

## 🔒 安全说明

- 修改默认密码和密钥
- 启用HTTPS（生产环境）
- 定期更新依赖包
- 配置防火墙规则
- 启用访问日志

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 支持

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-username/magnet-downloader/issues)
- 📖 文档: [项目文档](docs/)

## 🙏 致谢

感谢以下开源项目：

- [aria2](https://aria2.github.io/) - 下载引擎
- [Gin](https://gin-gonic.com/) - Web框架
- [GORM](https://gorm.io/) - ORM框架
- [React](https://reactjs.org/) - 前端框架
- [Ant Design](https://ant.design/) - UI组件库
