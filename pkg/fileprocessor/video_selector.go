package fileprocessor

import (
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"

	"magnet-downloader/pkg/logger"
)

// VideoFile 视频文件信息
type VideoFile struct {
	Path     string `json:"path"`
	Name     string `json:"name"`
	Size     int64  `json:"size"`
	IsSegment bool  `json:"is_segment"`
	SegmentIndex int `json:"segment_index"`
}

// VideoSelector 视频文件选择器
type VideoSelector struct {
	// 支持的视频格式
	videoExtensions []string
	// 分段文件模式
	segmentPatterns []*regexp.Regexp
	// 最小文件大小（字节）
	minFileSize int64
}

// NewVideoSelector 创建视频选择器
func NewVideoSelector() *VideoSelector {
	return &VideoSelector{
		videoExtensions: []string{
			".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", 
			".webm", ".m4v", ".3gp", ".ts", ".m2ts", ".vob",
		},
		segmentPatterns: []*regexp.Regexp{
			// 匹配 filename.part1.mp4, filename.part01.mp4 等
			regexp.MustCompile(`\.part\d+\.`),
			// 匹配 filename.001.mp4, filename.01.mp4 等
			regexp.MustCompile(`\.\d{2,3}\.`),
			// 匹配 filename_part1.mp4, filename_1.mp4 等
			regexp.MustCompile(`[_-](part)?\d+\.`),
			// 匹配 filename.cd1.mp4, filename.disc1.mp4 等
			regexp.MustCompile(`\.(cd|disc)\d+\.`),
		},
		minFileSize: 10 * 1024 * 1024, // 10MB最小文件大小
	}
}

// SelectVideosFromDirectory 从目录中选择需要上传的视频文件
func (vs *VideoSelector) SelectVideosFromDirectory(downloadDir string) ([]VideoFile, error) {
	logger.Infof("开始扫描下载目录: %s", downloadDir)

	// 获取所有子目录
	taskDirs, err := vs.getTaskDirectories(downloadDir)
	if err != nil {
		return nil, fmt.Errorf("获取任务目录失败: %w", err)
	}

	var selectedVideos []VideoFile

	// 遍历每个任务目录
	for _, taskDir := range taskDirs {
		logger.Infof("处理任务目录: %s", taskDir)
		
		videos, err := vs.selectVideosFromTaskDirectory(taskDir)
		if err != nil {
			logger.Errorf("处理任务目录失败 %s: %v", taskDir, err)
			continue
		}

		selectedVideos = append(selectedVideos, videos...)
	}

	logger.Infof("总共选择了 %d 个视频文件进行上传", len(selectedVideos))
	return selectedVideos, nil
}

// getTaskDirectories 获取所有任务目录
func (vs *VideoSelector) getTaskDirectories(downloadDir string) ([]string, error) {
	var taskDirs []string

	entries, err := os.ReadDir(downloadDir)
	if err != nil {
		return nil, err
	}

	for _, entry := range entries {
		if entry.IsDir() {
			taskDir := filepath.Join(downloadDir, entry.Name())
			taskDirs = append(taskDirs, taskDir)
		}
	}

	return taskDirs, nil
}

// selectVideosFromTaskDirectory 从单个任务目录中选择视频文件
func (vs *VideoSelector) selectVideosFromTaskDirectory(taskDir string) ([]VideoFile, error) {
	// 获取目录中的所有视频文件
	allVideos, err := vs.findAllVideoFiles(taskDir)
	if err != nil {
		return nil, err
	}

	if len(allVideos) == 0 {
		logger.Infof("目录 %s 中没有找到视频文件", taskDir)
		return nil, nil
	}

	logger.Infof("目录 %s 中找到 %d 个视频文件", taskDir, len(allVideos))

	// 分析文件类型和选择策略
	return vs.analyzeAndSelectVideos(allVideos)
}

// findAllVideoFiles 查找目录中的所有视频文件
func (vs *VideoSelector) findAllVideoFiles(dir string) ([]VideoFile, error) {
	var videos []VideoFile

	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 检查是否为视频文件
		if !vs.isVideoFile(path) {
			return nil
		}

		// 检查文件大小
		if info.Size() < vs.minFileSize {
			logger.Debugf("跳过小文件: %s (大小: %d bytes)", path, info.Size())
			return nil
		}

		// 检查是否为分段文件
		isSegment, segmentIndex := vs.isSegmentFile(path)

		video := VideoFile{
			Path:         path,
			Name:         info.Name(),
			Size:         info.Size(),
			IsSegment:    isSegment,
			SegmentIndex: segmentIndex,
		}

		videos = append(videos, video)
		return nil
	})

	return videos, err
}

// isVideoFile 检查文件是否为视频文件
func (vs *VideoSelector) isVideoFile(filePath string) bool {
	ext := strings.ToLower(filepath.Ext(filePath))
	for _, videoExt := range vs.videoExtensions {
		if ext == videoExt {
			return true
		}
	}
	return false
}

// isSegmentFile 检查文件是否为分段文件
func (vs *VideoSelector) isSegmentFile(filePath string) (bool, int) {
	fileName := strings.ToLower(filepath.Base(filePath))

	for _, pattern := range vs.segmentPatterns {
		if pattern.MatchString(fileName) {
			// 尝试提取分段索引
			segmentIndex := vs.extractSegmentIndex(fileName)
			return true, segmentIndex
		}
	}

	return false, 0
}

// extractSegmentIndex 提取分段索引
func (vs *VideoSelector) extractSegmentIndex(fileName string) int {
	// 简单的数字提取逻辑
	re := regexp.MustCompile(`\d+`)
	matches := re.FindAllString(fileName, -1)
	
	if len(matches) > 0 {
		// 取最后一个数字作为分段索引
		lastMatch := matches[len(matches)-1]
		if len(lastMatch) <= 3 { // 避免提取到年份等大数字
			var index int
			fmt.Sscanf(lastMatch, "%d", &index)
			return index
		}
	}
	
	return 0
}

// analyzeAndSelectVideos 分析并选择需要上传的视频文件
func (vs *VideoSelector) analyzeAndSelectVideos(videos []VideoFile) ([]VideoFile, error) {
	if len(videos) == 0 {
		return nil, nil
	}

	// 分离分段文件和非分段文件
	var segmentFiles []VideoFile
	var singleFiles []VideoFile

	for _, video := range videos {
		if video.IsSegment {
			segmentFiles = append(segmentFiles, video)
		} else {
			singleFiles = append(singleFiles, video)
		}
	}

	var selectedVideos []VideoFile

	// 处理非分段文件：选择最大的文件
	if len(singleFiles) > 0 {
		largestFile := vs.findLargestFile(singleFiles)
		selectedVideos = append(selectedVideos, largestFile)
		logger.Infof("选择最大的单个文件: %s (大小: %.2f MB)", 
			largestFile.Name, float64(largestFile.Size)/(1024*1024))
	}

	// 处理分段文件：按系列分组并选择每个系列
	if len(segmentFiles) > 0 {
		segmentSeries := vs.groupSegmentFiles(segmentFiles)
		for seriesName, series := range segmentSeries {
			// 对分段文件按索引排序
			sort.Slice(series, func(i, j int) bool {
				return series[i].SegmentIndex < series[j].SegmentIndex
			})
			
			selectedVideos = append(selectedVideos, series...)
			logger.Infof("选择分段文件系列: %s (%d 个文件)", seriesName, len(series))
		}
	}

	return selectedVideos, nil
}

// findLargestFile 找到最大的文件
func (vs *VideoSelector) findLargestFile(files []VideoFile) VideoFile {
	if len(files) == 0 {
		return VideoFile{}
	}

	largest := files[0]
	for _, file := range files[1:] {
		if file.Size > largest.Size {
			largest = file
		}
	}

	return largest
}

// groupSegmentFiles 将分段文件按系列分组
func (vs *VideoSelector) groupSegmentFiles(segmentFiles []VideoFile) map[string][]VideoFile {
	series := make(map[string][]VideoFile)

	for _, file := range segmentFiles {
		// 提取系列名称（去除分段标识）
		seriesName := vs.extractSeriesName(file.Name)
		series[seriesName] = append(series[seriesName], file)
	}

	return series
}

// extractSeriesName 提取系列名称
func (vs *VideoSelector) extractSeriesName(fileName string) string {
	// 移除分段标识，保留基础名称
	name := fileName
	
	// 移除扩展名
	ext := filepath.Ext(name)
	name = strings.TrimSuffix(name, ext)
	
	// 移除常见的分段标识
	patterns := []string{
		`\.part\d+$`,
		`\.\d{2,3}$`,
		`[_-](part)?\d+$`,
		`\.(cd|disc)\d+$`,
	}
	
	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		name = re.ReplaceAllString(name, "")
	}
	
	return name
}

// GetVideoSelectionSummary 获取视频选择摘要
func (vs *VideoSelector) GetVideoSelectionSummary(videos []VideoFile) string {
	if len(videos) == 0 {
		return "没有选择任何视频文件"
	}

	var totalSize int64
	segmentCount := 0
	singleCount := 0

	for _, video := range videos {
		totalSize += video.Size
		if video.IsSegment {
			segmentCount++
		} else {
			singleCount++
		}
	}

	summary := fmt.Sprintf("选择了 %d 个视频文件进行上传:\n", len(videos))
	summary += fmt.Sprintf("- 单个文件: %d 个\n", singleCount)
	summary += fmt.Sprintf("- 分段文件: %d 个\n", segmentCount)
	summary += fmt.Sprintf("- 总大小: %.2f GB\n", float64(totalSize)/(1024*1024*1024))

	return summary
}

// ValidateVideoFile 验证视频文件
func (vs *VideoSelector) ValidateVideoFile(filePath string) error {
	// 检查文件是否存在
	info, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		return fmt.Errorf("视频文件不存在: %s", filePath)
	}
	if err != nil {
		return fmt.Errorf("无法访问视频文件: %w", err)
	}

	// 检查是否为文件
	if info.IsDir() {
		return fmt.Errorf("路径是目录而不是文件: %s", filePath)
	}

	// 检查文件大小
	if info.Size() < vs.minFileSize {
		return fmt.Errorf("文件大小过小 (%.2f MB < %.2f MB): %s", 
			float64(info.Size())/(1024*1024), 
			float64(vs.minFileSize)/(1024*1024), 
			filePath)
	}

	// 检查文件格式
	if !vs.isVideoFile(filePath) {
		return fmt.Errorf("不支持的视频格式: %s", filePath)
	}

	return nil
}