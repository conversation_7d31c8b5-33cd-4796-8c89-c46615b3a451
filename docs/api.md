# API 文档

磁力下载器 RESTful API 接口文档

## 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **认证方式**: JWT Bearer Token
- **Content-Type**: `application/json`

## 认证

### 用户登录

```http
POST /auth/login
```

**请求体**:
```json
{
  "username": "admin",
  "password": "admin123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 86400,
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin"
    }
  }
}
```

### 刷新Token

```http
POST /auth/refresh
```

**请求头**:
```
Authorization: Bearer <refresh_token>
```

### 用户登出

```http
POST /auth/logout
```

**请求头**:
```
Authorization: Bearer <token>
```

## 任务管理

### 获取任务列表

```http
GET /tasks
```

**查询参数**:
- `page` (int): 页码，默认1
- `page_size` (int): 每页数量，默认20
- `status` (string): 任务状态筛选
- `priority` (string): 优先级筛选
- `search` (string): 搜索关键词

**响应**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "magnet_uri": "magnet:?xt=urn:btih:...",
        "task_name": "示例任务",
        "status": "running",
        "priority": "normal",
        "progress": 45.6,
        "download_speed": 1048576,
        "upload_speed": 102400,
        "total_size": 1073741824,
        "downloaded_size": 489626624,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T01:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20
  }
}
```

### 创建下载任务

```http
POST /tasks
```

**请求体**:
```json
{
  "magnet_uri": "magnet:?xt=urn:btih:...",
  "task_name": "我的下载任务",
  "save_path": "/downloads/movies",
  "priority": "normal"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "magnet_uri": "magnet:?xt=urn:btih:...",
    "task_name": "我的下载任务",
    "status": "pending",
    "priority": "normal",
    "save_path": "/downloads/movies",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 获取任务详情

```http
GET /tasks/{id}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "magnet_uri": "magnet:?xt=urn:btih:...",
    "task_name": "示例任务",
    "status": "running",
    "priority": "normal",
    "progress": 45.6,
    "download_speed": 1048576,
    "upload_speed": 102400,
    "total_size": 1073741824,
    "downloaded_size": 489626624,
    "save_path": "/downloads/movies",
    "aria2_gid": "2089b05ecca3d829",
    "error_message": null,
    "retry_count": 0,
    "max_retries": 3,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T01:00:00Z",
    "started_at": "2024-01-01T00:05:00Z",
    "completed_at": null
  }
}
```

### 更新任务

```http
PUT /tasks/{id}
```

**请求体**:
```json
{
  "task_name": "新的任务名称",
  "priority": "high"
}
```

### 删除任务

```http
DELETE /tasks/{id}
```

### 任务操作

#### 开始任务

```http
POST /tasks/{id}/start
```

#### 暂停任务

```http
POST /tasks/{id}/pause
```

#### 恢复任务

```http
POST /tasks/{id}/resume
```

#### 取消任务

```http
POST /tasks/{id}/cancel
```

#### 重试任务

```http
POST /tasks/{id}/retry
```

### 获取任务统计

```http
GET /tasks/stats
```

**响应**:
```json
{
  "success": true,
  "data": {
    "total_tasks": 150,
    "active_tasks": 5,
    "completed_tasks": 120,
    "failed_tasks": 10,
    "pending_tasks": 15,
    "total_downloaded": 107374182400,
    "average_speed": 2097152,
    "success_rate": 88.9
  }
}
```

## 用户管理

### 获取用户资料

```http
GET /users/profile
```

### 更新用户资料

```http
PUT /users/profile
```

**请求体**:
```json
{
  "email": "<EMAIL>"
}
```

### 修改密码

```http
PUT /users/password
```

**请求体**:
```json
{
  "old_password": "old123",
  "new_password": "new123"
}
```

## 管理员接口

### 用户管理

#### 获取用户列表

```http
GET /admin/users
```

#### 创建用户

```http
POST /admin/users
```

**请求体**:
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "user"
}
```

#### 更新用户

```http
PUT /admin/users/{id}
```

#### 删除用户

```http
DELETE /admin/users/{id}
```

### 系统配置

#### 获取配置列表

```http
GET /admin/config
```

#### 更新配置

```http
PUT /admin/config/{key}
```

**请求体**:
```json
{
  "value": "new_value"
}
```

### 调度器管理

#### 获取调度器状态

```http
GET /admin/scheduler/status
```

#### 获取调度任务列表

```http
GET /admin/scheduler/jobs
```

#### 验证Cron表达式

```http
POST /admin/scheduler/validate
```

**请求体**:
```json
{
  "spec": "0 */5 * * * *"
}
```

## WebSocket 接口

### 连接

```
ws://localhost:8080/api/v1/ws
```

### 认证

连接后发送认证消息：
```json
{
  "type": "auth",
  "token": "your-jwt-token"
}
```

### 消息类型

#### 任务进度更新

```json
{
  "type": "task_progress",
  "data": {
    "task_id": 1,
    "progress": 45.6,
    "download_speed": 1048576,
    "downloaded_size": 489626624
  }
}
```

#### 任务状态变更

```json
{
  "type": "task_status",
  "data": {
    "task_id": 1,
    "status": "completed",
    "message": "下载完成"
  }
}
```

#### 系统通知

```json
{
  "type": "system_notification",
  "data": {
    "title": "系统维护通知",
    "message": "系统将在30分钟后进行维护",
    "level": "warning"
  }
}
```

## 错误处理

### 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "请求参数无效",
    "details": {
      "field": "magnet_uri",
      "reason": "磁力链接格式不正确"
    }
  }
}
```

### 常见错误码

- `UNAUTHORIZED` (401): 未授权
- `FORBIDDEN` (403): 权限不足
- `NOT_FOUND` (404): 资源不存在
- `INVALID_REQUEST` (400): 请求参数无效
- `INTERNAL_ERROR` (500): 服务器内部错误
- `RATE_LIMITED` (429): 请求频率限制

## 状态码说明

### 任务状态

- `pending`: 等待中
- `running`: 下载中
- `paused`: 已暂停
- `completed`: 已完成
- `failed`: 失败
- `cancelled`: 已取消

### 任务优先级

- `low`: 低优先级
- `normal`: 普通优先级
- `high`: 高优先级
- `urgent`: 紧急优先级

### 用户角色

- `admin`: 管理员
- `user`: 普通用户
- `guest`: 访客

## 限流说明

- API请求限制: 100次/分钟
- 登录接口限制: 5次/分钟
- WebSocket连接限制: 10个/用户

## 示例代码

### JavaScript/Node.js

```javascript
const axios = require('axios');

// 登录获取token
const login = async () => {
  const response = await axios.post('http://localhost:8080/api/v1/auth/login', {
    username: 'admin',
    password: 'admin123'
  });
  return response.data.data.token;
};

// 创建下载任务
const createTask = async (token, magnetUri) => {
  const response = await axios.post('http://localhost:8080/api/v1/tasks', {
    magnet_uri: magnetUri,
    task_name: '我的任务'
  }, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.data.data;
};
```

### Python

```python
import requests

# 登录获取token
def login():
    response = requests.post('http://localhost:8080/api/v1/auth/login', json={
        'username': 'admin',
        'password': 'admin123'
    })
    return response.json()['data']['token']

# 创建下载任务
def create_task(token, magnet_uri):
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.post('http://localhost:8080/api/v1/tasks', 
        json={
            'magnet_uri': magnet_uri,
            'task_name': '我的任务'
        }, 
        headers=headers
    )
    return response.json()['data']
```
