import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  ApiResponse, 
  PaginatedResponse, 
  LoginRequest, 
  LoginResponse,
  DownloadTask,
  CreateTaskRequest,
  ListTasksRequest,
  User,
  SystemStats,
  SystemConfig
} from '../types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: '/api/v1',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器 - 添加认证token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器 - 处理通用错误
    this.api.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      async (error) => {
        if (error.response?.status === 401) {
          // Token过期，尝试刷新
          const refreshToken = localStorage.getItem('refresh_token');
          if (refreshToken) {
            try {
              const response = await this.refreshToken(refreshToken);
              localStorage.setItem('access_token', response.access_token);
              localStorage.setItem('refresh_token', response.refresh_token);
              
              // 重试原请求
              error.config.headers.Authorization = `Bearer ${response.access_token}`;
              return this.api.request(error.config);
            } catch (refreshError) {
              // 刷新失败，清除token并跳转到登录页
              this.clearAuth();
              window.location.href = '/login';
            }
          } else {
            this.clearAuth();
            window.location.href = '/login';
          }
        }
        return Promise.reject(error);
      }
    );
  }

  private clearAuth() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_info');
  }

  // 认证相关
  async login(data: LoginRequest): Promise<LoginResponse> {
    const response = await this.api.post<ApiResponse<LoginResponse>>('/auth/login', data);
    return response.data.data!;
  }

  async logout(): Promise<void> {
    await this.api.post('/auth/logout');
    this.clearAuth();
  }

  async refreshToken(refreshToken: string): Promise<{ access_token: string; refresh_token: string }> {
    const response = await this.api.post<ApiResponse<{ access_token: string; refresh_token: string }>>(
      '/auth/refresh', 
      { refresh_token: refreshToken }
    );
    return response.data.data!;
  }

  // 用户相关
  async getUserProfile(): Promise<User> {
    const response = await this.api.get<ApiResponse<User>>('/users/profile');
    return response.data.data!;
  }

  async updateUserProfile(data: Partial<User>): Promise<User> {
    const response = await this.api.put<ApiResponse<User>>('/users/profile', data);
    return response.data.data!;
  }

  async changePassword(data: { old_password: string; new_password: string }): Promise<void> {
    await this.api.put('/users/password', data);
  }

  // 任务相关
  async getTasks(params: ListTasksRequest = {}): Promise<PaginatedResponse<DownloadTask>> {
    const response = await this.api.get<ApiResponse<PaginatedResponse<DownloadTask>>>('/tasks', { params });
    return response.data.data!;
  }

  async getTask(id: number): Promise<DownloadTask> {
    const response = await this.api.get<ApiResponse<DownloadTask>>(`/tasks/${id}`);
    return response.data.data!;
  }

  async createTask(data: CreateTaskRequest): Promise<DownloadTask> {
    const response = await this.api.post<ApiResponse<DownloadTask>>('/tasks', data);
    return response.data.data!;
  }

  async updateTask(id: number, data: Partial<DownloadTask>): Promise<DownloadTask> {
    const response = await this.api.put<ApiResponse<DownloadTask>>(`/tasks/${id}`, data);
    return response.data.data!;
  }

  async deleteTask(id: number): Promise<void> {
    await this.api.delete(`/tasks/${id}`);
  }

  async startTask(id: number): Promise<void> {
    await this.api.post(`/tasks/${id}/start`);
  }

  async pauseTask(id: number): Promise<void> {
    await this.api.post(`/tasks/${id}/pause`);
  }

  async resumeTask(id: number): Promise<void> {
    await this.api.post(`/tasks/${id}/resume`);
  }

  async cancelTask(id: number): Promise<void> {
    await this.api.post(`/tasks/${id}/cancel`);
  }

  async retryTask(id: number): Promise<void> {
    await this.api.post(`/tasks/${id}/retry`);
  }

  async getTaskStats(): Promise<any> {
    const response = await this.api.get<ApiResponse<any>>('/tasks/stats');
    return response.data.data!;
  }

  // 系统相关
  async getSystemInfo(): Promise<any> {
    const response = await this.api.get<ApiResponse<any>>('/system/info');
    return response.data.data!;
  }

  async getSystemStats(): Promise<SystemStats> {
    const response = await this.api.get<ApiResponse<SystemStats>>('/system/stats');
    return response.data.data!;
  }

  async getSystemVersion(): Promise<any> {
    const response = await this.api.get<ApiResponse<any>>('/system/version');
    return response.data.data!;
  }

  // 配置相关
  async getConfigs(params: any = {}): Promise<PaginatedResponse<SystemConfig>> {
    const response = await this.api.get<ApiResponse<PaginatedResponse<SystemConfig>>>('/config', { params });
    return response.data.data!;
  }

  async getPublicConfigs(): Promise<SystemConfig[]> {
    const response = await this.api.get<ApiResponse<SystemConfig[]>>('/config/public');
    return response.data.data!;
  }

  // 管理员相关
  async getUsers(params: any = {}): Promise<PaginatedResponse<User>> {
    const response = await this.api.get<ApiResponse<PaginatedResponse<User>>>('/admin/users', { params });
    return response.data.data!;
  }

  async createUser(data: any): Promise<User> {
    const response = await this.api.post<ApiResponse<User>>('/admin/users', data);
    return response.data.data!;
  }

  async updateUser(id: number, data: any): Promise<User> {
    const response = await this.api.put<ApiResponse<User>>(`/admin/users/${id}`, data);
    return response.data.data!;
  }

  async deleteUser(id: number): Promise<void> {
    await this.api.delete(`/admin/users/${id}`);
  }

  async getUserStats(): Promise<any> {
    const response = await this.api.get<ApiResponse<any>>('/admin/users/stats');
    return response.data.data!;
  }

  // WebSocket相关
  async getWebSocketStats(): Promise<any> {
    const response = await this.api.get<ApiResponse<any>>('/admin/websocket/stats');
    return response.data.data!;
  }

  async getOnlineUsers(): Promise<any> {
    const response = await this.api.get<ApiResponse<any>>('/admin/websocket/online-users');
    return response.data.data!;
  }

  async sendBroadcastMessage(data: any): Promise<void> {
    await this.api.post('/admin/websocket/broadcast', data);
  }

  // 健康检查
  async healthCheck(): Promise<any> {
    const response = await this.api.get<ApiResponse<any>>('/system/health');
    return response.data.data!;
  }

  async ping(): Promise<any> {
    const response = await this.api.get<ApiResponse<any>>('/ping');
    return response.data.data!;
  }
}

export const apiService = new ApiService();
export default apiService;
