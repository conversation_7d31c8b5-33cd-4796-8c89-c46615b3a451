package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"magnet-downloader/pkg/imgbb"
	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/mixfile"
)

// 全局速率限制器
var (
	lastUploadTime = time.Now()
	uploadMutex    sync.Mutex
)

// smartRateLimit 智能速率限制
func smartRateLimit(workerID int) {
	uploadMutex.Lock()
	defer uploadMutex.Unlock()
	
	timeSinceLastUpload := time.Since(lastUploadTime)
	minInterval := 500 * time.Millisecond // 最小间隔0.5秒，充分利用带宽
	
	if timeSinceLastUpload < minInterval {
		waitTime := minInterval - timeSinceLastUpload
		time.Sleep(waitTime)
	}
	
	lastUploadTime = time.Now()
}

// VideoUploadResult 视频上传结果
type VideoUploadResult struct {
	VideoPath    string    `json:"video_path"`
	VideoName    string    `json:"video_name"`
	ChunkCount   int       `json:"chunk_count"`
	ChunkURLs    []string  `json:"chunk_urls"`
	IndexURL     string    `json:"index_url"`
	ShareCode    string    `json:"share_code"`
	PlayURL      string    `json:"play_url"`
	UploadTime   time.Time `json:"upload_time"`
	FileSize     int64     `json:"file_size"`
	Status       string    `json:"status"` // "uploading", "completed", "failed"
}

// UploadProgress 上传进度记录
type UploadProgress struct {
	TotalVideos     int                          `json:"total_videos"`
	CompletedVideos int64                        `json:"completed_videos"`
	VideoResults    map[string]*VideoUploadResult `json:"video_results"`
	LastUpdated     time.Time                    `json:"last_updated"`
	mutex           sync.RWMutex                 `json:"-"`
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: go run cmd/smart_upload.go <视频文件夹路径> [并发数]")
		fmt.Println("例如: go run cmd/smart_upload.go \"/www/wwwroot/JAVAPI.COM/downloads/20 最新流出，推特大奶网黄女神kitty付费作品，夏天要来了玩水水，泳池坏坏跟湿湿的淫水混合\" 2")
		return
	}

	videoDir := os.Args[1]
	concurrency := 3 // 默认3个并发，充分利用带宽
	if len(os.Args) > 2 {
		if c, err := fmt.Sscanf(os.Args[2], "%d", &concurrency); err != nil || c != 1 {
			fmt.Printf("⚠️ 无效的并发数，使用默认值: %d\n", concurrency)
		}
	}

	// 限制最大并发数
	if concurrency > 5 {
		concurrency = 5
		fmt.Printf("⚠️ 并发数过高，自动调整为: %d\n", concurrency)
	}

	fmt.Printf("🎬 视频上传到本地Telegraph图床\n")
	fmt.Printf("📁 视频目录: %s\n", videoDir)
	fmt.Printf("⚡ 并发线程: %d\n", concurrency)
	fmt.Printf("🌐 图床地址: http://localhost:3000\n")

	// 初始化日志
	logger.Init("info", "text")

	// 检查视频目录
	if _, err := os.Stat(videoDir); os.IsNotExist(err) {
		fmt.Printf("❌ 视频目录不存在: %s\n", videoDir)
		return
	}

	// 扫描视频文件
	fmt.Println("📋 扫描视频文件...")
	videoFiles, err := scanVideoFiles(videoDir)
	if err != nil {
		fmt.Printf("❌ 扫描视频失败: %v\n", err)
		return
	}

	if len(videoFiles) == 0 {
		fmt.Println("❌ 未找到视频文件")
		return
	}

	fmt.Printf("🎬 找到 %d 个视频文件\n", len(videoFiles))
	for i, video := range videoFiles {
		fmt.Printf("   %d. %s (%.2f MB)\n", i+1, filepath.Base(video), getFileSize(video))
	}

	// 创建本地图床客户端
	imgbbConfig := &imgbb.Config{
		APIKey:     "",  // 本地图床不需要API密钥
		BaseURL:    "http://localhost:3000",
		Timeout:    60 * time.Second,
		MaxRetries: 3,
	}
	imgbbClient := imgbb.NewClient(imgbbConfig)

	// 测试图床连接
	fmt.Println("📡 测试图床连接...")
	if err := imgbbClient.TestConnection(); err != nil {
		fmt.Printf("❌ 图床连接失败: %v\n", err)
		fmt.Println("💡 请确保Telegraph-Image Express服务正在运行 (http://localhost:3000)")
		return
	}
	fmt.Println("✅ 图床连接成功")

	// 加载或创建进度文件
	progressFile := filepath.Join(videoDir, "upload_progress.json")
	progress := loadProgress(progressFile, len(videoFiles))

	fmt.Printf("📊 当前进度: %d/%d 视频已完成\n",
		atomic.LoadInt64(&progress.CompletedVideos), progress.TotalVideos)

	if int(atomic.LoadInt64(&progress.CompletedVideos)) >= progress.TotalVideos {
		fmt.Println("✅ 所有视频已上传完成")
		displayResults(progress)
		return
	}

	// 准备未上传的视频
	var pendingVideos []string
	for _, videoFile := range videoFiles {
		videoName := filepath.Base(videoFile)
		
		progress.mutex.RLock()
		result, exists := progress.VideoResults[videoName]
		progress.mutex.RUnlock()

		if !exists || result.Status != "completed" {
			pendingVideos = append(pendingVideos, videoFile)
		}
	}

	fmt.Printf("🚀 开始上传 %d 个视频...\n", len(pendingVideos))

	// 上传视频
	uploadVideos(pendingVideos, progress, progressFile, concurrency, imgbbClient)

	// 保存最终进度
	saveProgress(progressFile, progress)

	// 显示结果
	displayResults(progress)
}

// scanVideoFiles 扫描视频文件
func scanVideoFiles(videoDir string) ([]string, error) {
	var videoFiles []string
	videoExtensions := []string{".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v"}

	err := filepath.Walk(videoDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() {
			ext := strings.ToLower(filepath.Ext(path))
			for _, videoExt := range videoExtensions {
				if ext == videoExt {
					videoFiles = append(videoFiles, path)
					break
				}
			}
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	// 按文件大小排序，优先处理大文件
	sort.Slice(videoFiles, func(i, j int) bool {
		info1, _ := os.Stat(videoFiles[i])
		info2, _ := os.Stat(videoFiles[j])
		return info1.Size() > info2.Size()
	})

	return videoFiles, nil
}

// getFileSize 获取文件大小(MB)
func getFileSize(filePath string) float64 {
	if info, err := os.Stat(filePath); err == nil {
		return float64(info.Size()) / (1024 * 1024)
	}
	return 0
}

// uploadVideos 上传视频
func uploadVideos(videoFiles []string, progress *UploadProgress, progressFile string, concurrency int, imgbbClient *imgbb.Client) {
	// 创建任务通道
	videoChan := make(chan string, concurrency)

	// 发送所有视频到通道
	go func() {
		for _, videoFile := range videoFiles {
			videoChan <- videoFile
		}
		close(videoChan)
	}()

	// 创建等待组
	var wg sync.WaitGroup

	// 启动工作协程
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for videoFile := range videoChan {
				uploadSingleVideo(workerID, videoFile, imgbbClient, progress)
				
				// 工作间隔，避免过载
				time.Sleep(3 * time.Second)
			}
		}(i)
	}

	// 启动进度保存协程
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for range ticker.C {
			current := atomic.LoadInt64(&progress.CompletedVideos)
			total := int64(progress.TotalVideos)

			if current >= total {
				break
			}

			saveProgress(progressFile, progress)
			fmt.Printf("📊 进度更新: %d/%d 视频已完成\n", current, total)
		}
	}()

	// 等待所有工作完成
	wg.Wait()
}

// uploadSingleVideo 上传单个视频
func uploadSingleVideo(workerID int, videoFile string, imgbbClient *imgbb.Client, progress *UploadProgress) {
	videoName := filepath.Base(videoFile)
	fmt.Printf("🎬 Worker-%d: 开始处理视频 %s\n", workerID, videoName)

	// 获取文件信息
	fileInfo, err := os.Stat(videoFile)
	if err != nil {
		fmt.Printf("❌ Worker-%d: 无法获取文件信息: %v\n", workerID, err)
		return
	}

	// 创建视频结果记录
	result := &VideoUploadResult{
		VideoPath:  videoFile,
		VideoName:  videoName,
		FileSize:   fileInfo.Size(),
		UploadTime: time.Now(),
		Status:     "uploading",
	}

	progress.mutex.Lock()
	progress.VideoResults[videoName] = result
	progress.mutex.Unlock()

	// 1. 处理视频文件 (分片和加密)
	fmt.Printf("📦 Worker-%d: 分片处理视频...\n", workerID)
	
	// 创建临时工作目录
	workDir := fmt.Sprintf("/tmp/video_upload_%d_%d", workerID, time.Now().Unix())
	os.MkdirAll(workDir, 0755)
	// 注意：不要在这里defer清理，需要在上传完成后手动清理

	processor := fileprocessor.NewProcessor(&fileprocessor.ProcessingConfig{
		ChunkSizeMB:       1,
		EncryptionEnabled: true,
		KeepOriginal:      true,  // 保留处理后的文件
		WorkDir:           workDir,
	})

	processResult, err := processor.ProcessFile(videoFile, func(stage string, progress float64, message string) {
		if progress == 1.0 { // 只在完成时显示
			fmt.Printf("   ✅ Worker-%d: %s 完成\n", workerID, stage)
		}
	})
	if err != nil {
		fmt.Printf("❌ Worker-%d: 视频处理失败: %v\n", workerID, err)
		result.Status = "failed"
		return
	}

	result.ChunkCount = processResult.ChunkCount
	fmt.Printf("📦 Worker-%d: 视频分片完成，共 %d 个分片\n", workerID, processResult.ChunkCount)
	fmt.Printf("🔍 Worker-%d: 工作目录: %s\n", workerID, processResult.WorkDir)

	// 2. 批量读取分片数据
	fmt.Printf("📦 Worker-%d: 读取分片数据...\n", workerID)
	
	encryptedDir := filepath.Join(processResult.WorkDir, "encrypted")
	var fileDataList []imgbb.FileData
	
	for i := 0; i < processResult.ChunkCount; i++ {
		// 构建加密分片文件路径
		chunkPath := filepath.Join(encryptedDir, fmt.Sprintf("chunk_%04d.bin.enc", i))
		
		// 读取分片数据
		chunkData, err := os.ReadFile(chunkPath)
		if err != nil {
			fmt.Printf("❌ Worker-%d: 读取分片 %d 失败: %v\n", workerID, i+1, err)
			result.Status = "failed"
			os.RemoveAll(workDir) // 清理临时目录
			return
		}

		// 准备文件数据
		chunkName := fmt.Sprintf("%s_chunk_%03d.png", 
			strings.TrimSuffix(videoName, filepath.Ext(videoName)), i)
		
		fileDataList = append(fileDataList, imgbb.FileData{
			Data:     chunkData,
			Filename: chunkName,
		})
	}

	fmt.Printf("⬆️ Worker-%d: 批量上传 %d 个分片到图床...\n", workerID, len(fileDataList))
	
	// 分批上传（每批最多10个文件，使用智能速率控制）
	batchSize := 10
	var chunkURLs []string
	
	for i := 0; i < len(fileDataList); i += batchSize {
		end := i + batchSize
		if end > len(fileDataList) {
			end = len(fileDataList)
		}
		
		batch := fileDataList[i:end]
		fmt.Printf("   📤 Worker-%d: 上传批次 %d-%d (%d个文件)...\n", workerID, i+1, end, len(batch))
		
		// 智能速率控制
		smartRateLimit(workerID)
		
		// 重试机制处理速率限制
		var batchResult *imgbb.BatchUploadResponse
		var err error
		maxRetries := 3
		
		for retry := 0; retry < maxRetries; retry++ {
			if retry > 0 {
				waitTime := 5 + retry*5 // 等待5-15秒，快速重试
				fmt.Printf("   ⏳ Worker-%d: 遇到速率限制，等待 %d 秒后重试...\n", workerID, waitTime)
				time.Sleep(time.Duration(waitTime) * time.Second)
			}
			
			batchResult, err = imgbbClient.BatchUploadData(batch)
			if err != nil {
				fmt.Printf("❌ Worker-%d: 批量上传失败 (尝试 %d/%d): %v\n", workerID, retry+1, maxRetries, err)
				continue
			}

			if batchResult.Failed > 0 {
				// 检查是否是速率限制错误
				hasRateLimit := false
				for _, errInfo := range batchResult.Errors {
					if strings.Contains(errInfo.Error, "Too Many Requests") {
						hasRateLimit = true
						break
					}
				}
				
				if hasRateLimit && retry < maxRetries-1 {
					fmt.Printf("   ⚠️ Worker-%d: 遇到速率限制 (尝试 %d/%d)，准备重试\n", workerID, retry+1, maxRetries)
					continue
				} else {
					fmt.Printf("❌ Worker-%d: 批量上传部分失败: 成功=%d, 失败=%d\n", 
						workerID, batchResult.Success, batchResult.Failed)
					for _, errInfo := range batchResult.Errors {
						fmt.Printf("   ❌ %s: %s\n", errInfo.Filename, errInfo.Error)
					}
					result.Status = "failed"
					os.RemoveAll(workDir) // 清理临时目录
					return
				}
			}
			
			// 成功，跳出重试循环
			break
		}
		
		if err != nil || batchResult.Failed > 0 {
			fmt.Printf("❌ Worker-%d: 批量上传最终失败\n", workerID)
			result.Status = "failed"
			os.RemoveAll(workDir) // 清理临时目录
			return
		}

		// 收集上传成功的URL
		for _, fileResult := range batchResult.Results {
			fullURL := fmt.Sprintf("%s%s", imgbbClient.GetBaseURL(), fileResult.Src)
			chunkURLs = append(chunkURLs, fullURL)
		}
		
		fmt.Printf("   ✅ Worker-%d: 批次上传成功 %d/%d 文件\n", workerID, batchResult.Success, batchResult.Total)
	}
	
	fmt.Printf("🎉 Worker-%d: 所有分片上传完成！总计 %d 个分片\n", workerID, len(chunkURLs))

	result.ChunkURLs = chunkURLs

	// 3. 创建索引文件
	fmt.Printf("📋 Worker-%d: 创建索引文件...\n", workerID)
	
	indexProcessor := mixfile.NewIndexProcessor()
	index, err := indexProcessor.ProcessingResultToIndex(processResult, chunkURLs)
	if err != nil {
		fmt.Printf("❌ Worker-%d: 创建索引失败: %v\n", workerID, err)
		result.Status = "failed"
		return
	}

	// 序列化、压缩、加密索引
	indexManager := mixfile.NewIndexManager()
	indexData, err := indexManager.SerializeIndex(index)
	if err != nil {
		fmt.Printf("❌ Worker-%d: 序列化索引失败: %v\n", workerID, err)
		result.Status = "failed"
		return
	}

	compressedIndex, err := indexManager.CompressIndex(indexData)
	if err != nil {
		fmt.Printf("❌ Worker-%d: 压缩索引失败: %v\n", workerID, err)
		result.Status = "failed"
		return
	}

	// 使用固定密钥加密索引
	encryptionKey := []byte("0123456789abcdef0123456789abcdef")
	encryptedIndex, err := indexManager.EncryptIndex(compressedIndex, encryptionKey)
	if err != nil {
		fmt.Printf("❌ Worker-%d: 加密索引失败: %v\n", workerID, err)
		result.Status = "failed"
		return
	}

	// 4. 上传索引文件
	fmt.Printf("📤 Worker-%d: 上传索引文件...\n", workerID)
	
	indexFilename := fmt.Sprintf("index_%s.json", 
		strings.TrimSuffix(videoName, filepath.Ext(videoName)))
	
	indexUploadResult, err := imgbbClient.UploadWithSteganography(encryptedIndex, indexFilename)
	if err != nil || !indexUploadResult.Success {
		fmt.Printf("❌ Worker-%d: 索引上传失败: %v\n", workerID, err)
		result.Status = "failed"
		return
	}

	result.IndexURL = indexUploadResult.URL

	// 5. 生成分享码和播放链接
	fmt.Printf("🔗 Worker-%d: 生成播放链接...\n", workerID)
	
	// 生成分享码 (使用索引URL的一部分)
	shareCode := generateShareCode(indexUploadResult.URL)
	result.ShareCode = shareCode
	
	// 生成播放链接 (假设有一个播放器页面)
	result.PlayURL = fmt.Sprintf("http://localhost:8080/play?code=%s", shareCode)

	// 6. 完成
	result.Status = "completed"
	atomic.AddInt64(&progress.CompletedVideos, 1)

	fmt.Printf("🎉 Worker-%d: 视频 %s 上传完成！\n", workerID, videoName)
	fmt.Printf("   📎 播放链接: %s\n", result.PlayURL)
	fmt.Printf("   🔑 分享码: %s\n", result.ShareCode)
	fmt.Printf("   📦 分片数: %d\n", result.ChunkCount)
	fmt.Printf("   📏 文件大小: %.2f MB\n", float64(result.FileSize)/(1024*1024))

	// 清理临时工作目录
	os.RemoveAll(workDir)
}

// generateShareCode 生成分享码
func generateShareCode(indexURL string) string {
	// 从URL中提取文件ID作为分享码
	parts := strings.Split(indexURL, "/")
	if len(parts) > 0 {
		filename := parts[len(parts)-1]
		// 移除扩展名，取前16个字符作为分享码
		code := strings.TrimSuffix(filename, filepath.Ext(filename))
		if len(code) > 16 {
			return code[:16]
		}
		return code
	}
	return fmt.Sprintf("share_%d", time.Now().Unix())
}

// loadProgress 加载上传进度
func loadProgress(progressFile string, totalVideos int) *UploadProgress {
	progress := &UploadProgress{
		TotalVideos:     totalVideos,
		CompletedVideos: 0,
		VideoResults:    make(map[string]*VideoUploadResult),
		LastUpdated:     time.Now(),
	}
	
	if data, err := os.ReadFile(progressFile); err == nil {
		json.Unmarshal(data, progress)
		progress.TotalVideos = totalVideos
		
		// 统计已完成的视频
		completed := int64(0)
		for _, result := range progress.VideoResults {
			if result.Status == "completed" {
				completed++
			}
		}
		atomic.StoreInt64(&progress.CompletedVideos, completed)
	}
	
	return progress
}

// saveProgress 保存上传进度
func saveProgress(progressFile string, progress *UploadProgress) {
	progress.mutex.RLock()
	defer progress.mutex.RUnlock()
	
	progress.LastUpdated = time.Now()
	data, _ := json.MarshalIndent(progress, "", "  ")
	os.WriteFile(progressFile, data, 0644)
}

// displayResults 显示上传结果
func displayResults(progress *UploadProgress) {
	fmt.Printf("\n🎬 视频上传结果汇总:\n")
	fmt.Printf("=" + strings.Repeat("=", 80) + "\n")

	progress.mutex.RLock()
	defer progress.mutex.RUnlock()

	completedCount := 0
	failedCount := 0

	for _, result := range progress.VideoResults {
		if result.Status == "completed" {
			completedCount++
			fmt.Printf("✅ %s\n", result.VideoName)
			fmt.Printf("   📎 播放链接: %s\n", result.PlayURL)
			fmt.Printf("   🔑 分享码: %s\n", result.ShareCode)
			fmt.Printf("   📦 分片数: %d\n", result.ChunkCount)
			fmt.Printf("   📏 文件大小: %.2f MB\n", float64(result.FileSize)/(1024*1024))
			fmt.Printf("   ⏱️ 上传时间: %s\n", result.UploadTime.Format("2006-01-02 15:04:05"))
			fmt.Println()
		} else if result.Status == "failed" {
			failedCount++
			fmt.Printf("❌ %s (失败)\n", result.VideoName)
		}
	}

	fmt.Printf("📊 总结: 成功 %d 个，失败 %d 个\n", completedCount, failedCount)
	
	if completedCount > 0 {
		fmt.Printf("\n💡 使用说明:\n")
		fmt.Printf("   - 复制播放链接到浏览器即可观看\n")
		fmt.Printf("   - 分享码可用于快速访问视频\n")
		fmt.Printf("   - 所有视频已加密存储，安全可靠\n")
		fmt.Printf("   - 视频通过本地Telegraph图床存储\n")
	}
}