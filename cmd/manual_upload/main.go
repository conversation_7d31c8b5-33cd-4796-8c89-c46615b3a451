package main

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/database"
	"magnet-downloader/internal/model"
	"magnet-downloader/internal/service"
	"magnet-downloader/internal/websocket"
	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/imgbb"
	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/streaming"
)

const (
	DOWNLOADS_DIR = "/www/wwwroot/JAVAPI.COM/downloads"
)

// FileInfo 文件信息
type FileInfo struct {
	Path string
	Size int64
	Name string
}

// MovieFolder 影片文件夹信息
type MovieFolder struct {
	Path      string
	Name      string
	MainFile  *FileInfo
	AllFiles  []FileInfo
	HasParts  bool // 是否有分段文件
}

func main() {
	fmt.Println("🎬 手动触发影片上传流程")
	fmt.Println(strings.Repeat("=", 60))

	// 初始化日志
	logger.Init("info", "text")

	// 初始化配置
	cfg := config.Load()

	// 初始化数据库
	database.Init(cfg.Database)

	// 扫描downloads目录
	fmt.Printf("📁 扫描目录: %s\n", DOWNLOADS_DIR)
	movieFolders, err := scanMovieFolders(DOWNLOADS_DIR)
	if err != nil {
		fmt.Printf("❌ 扫描目录失败: %v\n", err)
		return
	}

	if len(movieFolders) == 0 {
		fmt.Println("📭 没有找到影片文件夹")
		return
	}

	// 显示找到的影片
	fmt.Printf("\n🎬 找到 %d 个影片文件夹:\n", len(movieFolders))
	for i, folder := range movieFolders {
		fmt.Printf("%d. %s\n", i+1, folder.Name)
		if folder.MainFile != nil {
			fmt.Printf("   📹 主文件: %s (%.2f GB)\n", 
				folder.MainFile.Name, float64(folder.MainFile.Size)/1024/1024/1024)
		}
		if folder.HasParts {
			fmt.Printf("   📦 包含 %d 个分段文件\n", len(folder.AllFiles))
		}
		fmt.Printf("   📍 路径: %s\n", folder.Path)
		fmt.Println()
	}

	// 用户选择
	scanner := bufio.NewScanner(os.Stdin)
	fmt.Print("请选择要上传的影片 (输入序号，0=全部): ")
	scanner.Scan()
	choice := strings.TrimSpace(scanner.Text())

	if choice == "0" {
		// 上传全部
		fmt.Println("\n🚀 开始上传所有影片...")
		for i, folder := range movieFolders {
			fmt.Printf("\n📤 上传影片 %d/%d: %s\n", i+1, len(movieFolders), folder.Name)
			if err := processMovieFolder(folder, cfg); err != nil {
				fmt.Printf("❌ 上传失败: %v\n", err)
			} else {
				fmt.Printf("✅ 上传成功\n")
			}
		}
	} else {
		// 上传单个
		index, err := strconv.Atoi(choice)
		if err != nil || index < 1 || index > len(movieFolders) {
			fmt.Println("❌ 无效的选择")
			return
		}

		folder := movieFolders[index-1]
		fmt.Printf("\n📤 上传影片: %s\n", folder.Name)
		if err := processMovieFolder(folder, cfg); err != nil {
			fmt.Printf("❌ 上传失败: %v\n", err)
		} else {
			fmt.Printf("✅ 上传成功\n")
		}
	}

	fmt.Println("\n🎉 上传流程完成！")
}

// scanMovieFolders 扫描影片文件夹
func scanMovieFolders(downloadsDir string) ([]*MovieFolder, error) {
	var movieFolders []*MovieFolder

	entries, err := os.ReadDir(downloadsDir)
	if err != nil {
		return nil, fmt.Errorf("读取目录失败: %w", err)
	}

	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		folderPath := filepath.Join(downloadsDir, entry.Name())
		folder, err := analyzeMovieFolder(folderPath, entry.Name())
		if err != nil {
			fmt.Printf("⚠️ 分析文件夹失败 %s: %v\n", entry.Name(), err)
			continue
		}

		if folder != nil {
			movieFolders = append(movieFolders, folder)
		}
	}

	return movieFolders, nil
}

// analyzeMovieFolder 分析影片文件夹
func analyzeMovieFolder(folderPath, folderName string) (*MovieFolder, error) {
	var allFiles []FileInfo

	err := filepath.Walk(folderPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		// 只处理视频文件
		if isVideoFile(info.Name()) {
			allFiles = append(allFiles, FileInfo{
				Path: path,
				Size: info.Size(),
				Name: info.Name(),
			})
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	if len(allFiles) == 0 {
		return nil, nil // 没有视频文件
	}

	// 按文件大小排序（降序）
	sort.Slice(allFiles, func(i, j int) bool {
		return allFiles[i].Size > allFiles[j].Size
	})

	folder := &MovieFolder{
		Path:     folderPath,
		Name:     folderName,
		AllFiles: allFiles,
	}

	// 分析文件结构
	if len(allFiles) == 1 {
		// 单个文件
		folder.MainFile = &allFiles[0]
		folder.HasParts = false
	} else {
		// 多个文件，需要判断是否为分段文件
		folder.HasParts = hasPartFiles(allFiles)
		
		if folder.HasParts {
			// 有分段文件，选择最大的作为主文件（通常是合并后的文件）
			folder.MainFile = &allFiles[0]
		} else {
			// 没有分段文件，选择最大的文件
			folder.MainFile = &allFiles[0]
		}
	}

	return folder, nil
}

// isVideoFile 检查是否为视频文件
func isVideoFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	videoExtensions := []string{
		".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", 
		".webm", ".m4v", ".3gp", ".ts", ".m2ts", ".mts",
	}

	for _, videoExt := range videoExtensions {
		if ext == videoExt {
			return true
		}
	}

	return false
}

// hasPartFiles 检查是否有分段文件
func hasPartFiles(files []FileInfo) bool {
	// 检查文件名模式
	for _, file := range files {
		name := strings.ToLower(file.Name)
		
		// 常见的分段文件模式
		patterns := []string{
			".part", ".001", ".002", ".003",
			"cd1", "cd2", "part1", "part2",
			"disc1", "disc2",
		}
		
		for _, pattern := range patterns {
			if strings.Contains(name, pattern) {
				return true
			}
		}
	}

	// 检查文件大小差异
	if len(files) > 1 {
		largestSize := files[0].Size
		for i := 1; i < len(files); i++ {
			// 如果最大文件比其他文件大很多，可能是合并后的文件
			ratio := float64(largestSize) / float64(files[i].Size)
			if ratio > 5.0 { // 大5倍以上
				return true
			}
		}
	}

	return false
}

// processMovieFolder 处理影片文件夹
func processMovieFolder(folder *MovieFolder, cfg *config.Config) error {
	if folder.MainFile == nil {
		return fmt.Errorf("没有找到主文件")
	}

	fmt.Printf("📹 处理文件: %s (%.2f GB)\n", 
		folder.MainFile.Name, float64(folder.MainFile.Size)/1024/1024/1024)

	// 创建下载任务记录
	task := &model.DownloadTask{
		TaskName:    folder.Name,
		MagnetURI:   "manual://upload", // 标记为手动上传
		Status:      model.TaskStatusCompleted,
		TotalSize:   folder.MainFile.Size,
		Progress:    100.0,
		CreatedAt:   time.Now(),
	}
	
	// 设置完成时间
	now := time.Now()
	task.CompletedAt = &now

	// 保存到数据库
	db := database.GetDB()
	if err := db.Create(task).Error; err != nil {
		return fmt.Errorf("创建任务记录失败: %w", err)
	}

	fmt.Printf("📝 创建任务记录: ID=%d\n", task.ID)

	// 更新实际文件信息
	actualFiles := model.ActualFiles{
		{
			Path: folder.MainFile.Path,
			Size: folder.MainFile.Size,
			Name: folder.MainFile.Name,
		},
	}
	task.ActualFiles = actualFiles

	if err := db.Save(task).Error; err != nil {
		return fmt.Errorf("更新任务文件信息失败: %w", err)
	}

	// 创建文件处理服务
	fileProcessingService := createFileProcessingService(cfg)

	// 开始文件处理
	fmt.Println("🔄 开始文件处理...")
	if err := fileProcessingService.StartProcessing(task.ID); err != nil {
		return fmt.Errorf("启动文件处理失败: %w", err)
	}

	// 等待处理完成
	fmt.Println("⏳ 等待处理完成...")
	if err := waitForProcessingComplete(task.ID, db); err != nil {
		return fmt.Errorf("等待处理完成失败: %w", err)
	}

	// 获取最终结果
	if err := db.First(task, task.ID).Error; err != nil {
		return fmt.Errorf("获取最终结果失败: %w", err)
	}

	fmt.Printf("🎉 处理完成！\n")
	if task.ShareCode != "" {
		fmt.Printf("🔗 分享码: %s\n", task.ShareCode)
	}
	if task.IndexURL != "" {
		fmt.Printf("📋 索引URL: %s\n", task.IndexURL)
	}

	return nil
}

// createFileProcessingService 创建文件处理服务
func createFileProcessingService(cfg *config.Config) service.FileProcessingService {
	// 创建WebSocket服务（简化版）
	websocketSvc := &websocket.Service{} // 简化实现

	// 文件处理器配置
	processorConfig := &fileprocessor.ProcessingConfig{
		ChunkSizeMB:       cfg.FileProcessing.ChunkSizeMB,
		EncryptionEnabled: cfg.FileProcessing.EncryptionEnabled,
		WorkDir:           cfg.FileProcessing.WorkDir,
	}

	// ImgBB配置
	imgbbConfig := &imgbb.Config{
		APIKey: cfg.FileProcessing.ImgBB.APIKey,
	}

	// 播放列表配置
	playlistConfig := &streaming.PlaylistConfig{
		Version:        cfg.FileProcessing.Playlist.Version,
		TargetDuration: cfg.FileProcessing.Playlist.TargetDuration,
		AllowCache:     cfg.FileProcessing.Playlist.AllowCache,
		PlaylistType:   cfg.FileProcessing.Playlist.PlaylistType,
	}

	// MixFile配置
	mixFileConfig := &config.MixFileConfig{
		Enabled:                cfg.FileProcessing.MixFile.Enabled,
		EnableSteganography:    cfg.FileProcessing.MixFile.EnableSteganography,
		EnableIndexCompression: cfg.FileProcessing.MixFile.EnableIndexCompression,
		EnableIndexEncryption:  cfg.FileProcessing.MixFile.EnableIndexEncryption,
		ShareCodePrefix:        cfg.FileProcessing.MixFile.ShareCodePrefix,
		MaxShareCodeLength:     cfg.FileProcessing.MixFile.MaxShareCodeLength,
		IndexUploadRetries:     cfg.FileProcessing.MixFile.IndexUploadRetries,
	}

	return service.NewFileProcessingService(
		database.GetDB(),
		websocketSvc,
		processorConfig,
		imgbbConfig,
		playlistConfig,
		mixFileConfig,
	)
}

// waitForProcessingComplete 等待处理完成
func waitForProcessingComplete(taskID uint, db *database.DB) error {
	timeout := time.After(30 * time.Minute) // 30分钟超时
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return fmt.Errorf("处理超时")
		case <-ticker.C:
			var task model.DownloadTask
			if err := db.First(&task, taskID).Error; err != nil {
				return fmt.Errorf("查询任务状态失败: %w", err)
			}

			fmt.Printf("📊 处理状态: %s (%.1f%%)\n", task.ProcessingStatus, task.ProcessingProgress)

			if task.Status == model.TaskStatusReady {
				return nil // 处理完成
			}

			if task.Status == model.TaskStatusError {
				return fmt.Errorf("处理失败: %s", task.ErrorMessage)
			}
		}
	}
}